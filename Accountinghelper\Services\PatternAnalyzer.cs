using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Accountinghelper.Models;

namespace Accountinghelper.Services
{
    public class PatternAnalyzer
    {
        private readonly ExcelDataService _excelService;

        public PatternAnalyzer()
        {
            _excelService = new ExcelDataService();
        }

        public List<SuggestedRule> AnalyzeReferenceFiles(string directoryPath)
        {
            var suggestedRules = new List<SuggestedRule>();

            try
            {
                var referenceFiles = Directory.GetFiles(directoryPath, "reference*.xlsx")
                    .Concat(Directory.GetFiles(directoryPath, "reference*.xls"))
                    .ToList();

                foreach (var filePath in referenceFiles)
                {
                    var patterns = AnalyzeReferenceFile(filePath);
                    suggestedRules.AddRange(patterns);
                }

                // Group similar patterns and calculate confidence scores
                var groupedRules = GroupAndScoreRules(suggestedRules);
                return groupedRules.OrderByDescending(r => r.ConfidenceScore).ToList();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error analyzing reference files: {ex.Message}", ex);
            }
        }

        private List<SuggestedRule> AnalyzeReferenceFile(string filePath)
        {
            var suggestedRules = new List<SuggestedRule>();

            try
            {
                using var excelService = new ExcelDataService();
                var transactions = excelService.LoadExcelFile(filePath);

                // Group transactions by their comments
                var commentGroups = transactions
                    .Where(t => !string.IsNullOrWhiteSpace(t.MyComments))
                    .GroupBy(t => t.MyComments.Trim())
                    .Where(g => g.Count() >= 2) // Only consider patterns that appear at least twice
                    .ToList();

                foreach (var group in commentGroups)
                {
                    var comment = group.Key;
                    var transactionsWithComment = group.ToList();

                    // Analyze patterns in TransactionRemarks
                    var remarksPatterns = FindCommonPatterns(
                        transactionsWithComment.Select(t => t.TransactionRemarks).ToList());

                    foreach (var pattern in remarksPatterns)
                    {
                        suggestedRules.Add(new SuggestedRule
                        {
                            Comment = comment,
                            Pattern = pattern,
                            ColumnName = "TransactionRemarks",
                            Operator = ComparisonOperator.CONTAINS,
                            SampleTransactions = transactionsWithComment.Take(3).ToList(),
                            Frequency = transactionsWithComment.Count,
                            SourceFile = Path.GetFileName(filePath)
                        });
                    }

                    // Analyze patterns in Description
                    var descriptionPatterns = FindCommonPatterns(
                        transactionsWithComment.Select(t => t.Description).ToList());

                    foreach (var pattern in descriptionPatterns)
                    {
                        suggestedRules.Add(new SuggestedRule
                        {
                            Comment = comment,
                            Pattern = pattern,
                            ColumnName = "Description",
                            Operator = ComparisonOperator.CONTAINS,
                            SampleTransactions = transactionsWithComment.Take(3).ToList(),
                            Frequency = transactionsWithComment.Count,
                            SourceFile = Path.GetFileName(filePath)
                        });
                    }

                    // Analyze amount ranges
                    var amounts = transactionsWithComment
                        .Where(t => t.Amount.HasValue)
                        .Select(t => t.Amount!.Value)
                        .ToList();

                    if (amounts.Count >= 2)
                    {
                        var minAmount = amounts.Min();
                        var maxAmount = amounts.Max();

                        if (minAmount == maxAmount)
                        {
                            // Exact amount match
                            suggestedRules.Add(new SuggestedRule
                            {
                                Comment = comment,
                                Pattern = minAmount.ToString(),
                                ColumnName = "Amount",
                                Operator = ComparisonOperator.EQUALS,
                                SampleTransactions = transactionsWithComment.Take(3).ToList(),
                                Frequency = transactionsWithComment.Count,
                                SourceFile = Path.GetFileName(filePath)
                            });
                        }
                    }
                }

                return suggestedRules;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error analyzing file {filePath}: {ex.Message}", ex);
            }
        }

        private List<string> FindCommonPatterns(List<string> texts)
        {
            var patterns = new List<string>();
            var cleanTexts = texts.Where(t => !string.IsNullOrWhiteSpace(t)).ToList();

            if (cleanTexts.Count < 2)
                return patterns;

            // Find common words (3+ characters)
            var allWords = cleanTexts
                .SelectMany(t => t.Split(' ', StringSplitOptions.RemoveEmptyEntries))
                .Where(w => w.Length >= 3)
                .GroupBy(w => w.ToLowerInvariant())
                .Where(g => g.Count() >= Math.Max(2, cleanTexts.Count * 0.5)) // Appears in at least 50% of texts
                .Select(g => g.Key)
                .ToList();

            patterns.AddRange(allWords);

            // Find common substrings (5+ characters)
            var commonSubstrings = FindCommonSubstrings(cleanTexts, 5);
            patterns.AddRange(commonSubstrings);

            return patterns.Distinct().ToList();
        }

        private List<string> FindCommonSubstrings(List<string> texts, int minLength)
        {
            var substrings = new Dictionary<string, int>();

            foreach (var text in texts)
            {
                var lowerText = text.ToLowerInvariant();
                for (int i = 0; i <= lowerText.Length - minLength; i++)
                {
                    for (int length = minLength; length <= lowerText.Length - i; length++)
                    {
                        var substring = lowerText.Substring(i, length);
                        if (substring.Trim().Length == substring.Length) // No leading/trailing spaces
                        {
                            substrings[substring] = substrings.GetValueOrDefault(substring, 0) + 1;
                        }
                    }
                }
            }

            return substrings
                .Where(kvp => kvp.Value >= Math.Max(2, texts.Count * 0.5))
                .Select(kvp => kvp.Key)
                .ToList();
        }

        private List<SuggestedRule> GroupAndScoreRules(List<SuggestedRule> rules)
        {
            var groupedRules = new List<SuggestedRule>();

            var ruleGroups = rules
                .GroupBy(r => new { r.Comment, r.ColumnName, r.Pattern, r.Operator })
                .ToList();

            foreach (var group in ruleGroups)
            {
                var combinedRule = group.First();
                combinedRule.Frequency = group.Sum(r => r.Frequency);
                combinedRule.SampleTransactions = group
                    .SelectMany(r => r.SampleTransactions)
                    .DistinctBy(t => t.RowIndex)
                    .Take(5)
                    .ToList();

                // Calculate confidence score based on frequency and pattern quality
                var patternLength = combinedRule.Pattern.Length;
                var frequencyScore = Math.Min(combinedRule.Frequency / 10.0, 1.0); // Max 1.0 for 10+ occurrences
                var lengthScore = Math.Min(patternLength / 20.0, 1.0); // Max 1.0 for 20+ character patterns
                var sourceScore = group.Select(r => r.SourceFile).Distinct().Count() / 5.0; // Bonus for multiple sources

                combinedRule.ConfidenceScore = (frequencyScore * 0.5 + lengthScore * 0.3 + sourceScore * 0.2);

                groupedRules.Add(combinedRule);
            }

            return groupedRules;
        }
    }

    public class SuggestedRule
    {
        public string Comment { get; set; } = string.Empty;
        public string Pattern { get; set; } = string.Empty;
        public string ColumnName { get; set; } = string.Empty;
        public ComparisonOperator Operator { get; set; }
        public List<BankTransaction> SampleTransactions { get; set; } = new();
        public int Frequency { get; set; }
        public double ConfidenceScore { get; set; }
        public string SourceFile { get; set; } = string.Empty;

        public ClassificationRule ToClassificationRule()
        {
            return new ClassificationRule
            {
                Name = $"Auto-generated: {Comment}",
                Description = $"Generated from pattern analysis (Confidence: {ConfidenceScore:P1})",
                Comment = Comment,
                ConfidenceScore = ConfidenceScore,
                Conditions = new List<RuleCondition>
                {
                    new RuleCondition
                    {
                        ColumnName = ColumnName,
                        Operator = Operator,
                        Value = Pattern,
                        IsCaseSensitive = false
                    }
                }
            };
        }
    }
}
