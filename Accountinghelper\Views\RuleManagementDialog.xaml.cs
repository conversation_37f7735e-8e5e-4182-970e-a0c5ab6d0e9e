using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using Microsoft.Win32;
using Accountinghelper.Models;
using Accountinghelper.Services;

namespace Accountinghelper.Views
{
    public partial class RuleManagementDialog : Window
    {
        private readonly RuleManager _ruleManager;
        private readonly List<BankTransaction>? _transactionData;
        private ObservableCollection<ClassificationRule> _rules;
        private ClassificationRule? _selectedRule;

        // Drag and drop support
        private bool _isDragging = false;
        private Point _startPoint;
        private ClassificationRule? _draggedRule;

        // Inline editing support
        private bool _hasUnsavedChanges = false;

        public RuleManagementDialog(RuleManager ruleManager, List<BankTransaction>? transactionData = null)
        {
            InitializeComponent();
            _ruleManager = ruleManager;
            _transactionData = transactionData;
            _rules = new ObservableCollection<ClassificationRule>();
            LoadRules();
        }

        private void LoadRules()
        {
            _rules.Clear();
            var rules = _ruleManager.GetRulesByPriority();
            foreach (var rule in rules)
            {
                _rules.Add(rule);
            }
            RulesDataGrid.ItemsSource = _rules;
        }

        private void RulesDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedRule = RulesDataGrid.SelectedItem as ClassificationRule;
            UpdateRuleDetails();
            UpdateButtonStates();
        }

        private void UpdateRuleDetails()
        {
            if (_selectedRule == null)
            {
                RuleNameText.Text = "No rule selected";
                RuleDescriptionText.Text = "";
                RuleConditionsText.Text = "";
                return;
            }

            RuleNameText.Text = $"Name: {_selectedRule.Name}";
            RuleDescriptionText.Text = $"Description: {_selectedRule.Description}";
            
            var conditionsText = "Conditions: ";
            if (_selectedRule.Conditions.Any())
            {
                var conditionStrings = _selectedRule.Conditions.Select(c => c.ToString());
                conditionsText += string.Join($" {_selectedRule.LogicalOperator} ", conditionStrings);
            }
            else
            {
                conditionsText += "No conditions defined";
            }
            
            RuleConditionsText.Text = conditionsText;
        }

        private void UpdateButtonStates()
        {
            bool hasSelection = _selectedRule != null;
            EditRuleButton.IsEnabled = hasSelection;
            DeleteRuleButton.IsEnabled = hasSelection;
            TestRuleButton.IsEnabled = hasSelection;
            DuplicateRuleButton.IsEnabled = hasSelection;
            SaveChangesButton.IsEnabled = _hasUnsavedChanges;
        }

        private void NewRule_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new RuleCreationDialog(_ruleManager, null, _transactionData);
            if (dialog.ShowDialog() == true)
            {
                LoadRules();
            }
        }

        private void EditRule_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedRule == null) return;

            var dialog = new RuleCreationDialog(_ruleManager, _selectedRule, _transactionData);
            if (dialog.ShowDialog() == true)
            {
                LoadRules();
            }
        }

        private void DeleteRule_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedRule == null) return;

            var result = MessageBox.Show(
                $"Are you sure you want to delete the rule '{_selectedRule.Name}'?",
                "Confirm Delete",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                _ruleManager.DeleteRule(_selectedRule.Id);
                LoadRules();
            }
        }

        private void TestRule_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedRule == null) return;

            var dialog = new RuleTestDialog(_selectedRule, _transactionData);
            dialog.Owner = this;
            dialog.ShowDialog();
        }

        private void ExportRules_Click(object sender, RoutedEventArgs e)
        {
            var saveFileDialog = new SaveFileDialog
            {
                Title = "Export Rules",
                Filter = "JSON Files (*.json)|*.json|All Files (*.*)|*.*",
                FilterIndex = 1,
                FileName = "rules.json"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    _ruleManager.ExportRules(saveFileDialog.FileName);
                    MessageBox.Show("Rules exported successfully!", "Export Complete", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error exporting rules: {ex.Message}", "Export Error", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void ImportRules_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "Import Rules",
                Filter = "JSON Files (*.json)|*.json|All Files (*.*)|*.*",
                FilterIndex = 1
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    _ruleManager.ImportRules(openFileDialog.FileName);
                    LoadRules();
                    MessageBox.Show("Rules imported successfully!", "Import Complete", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error importing rules: {ex.Message}", "Import Error", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            if (_hasUnsavedChanges)
            {
                var result = MessageBox.Show(
                    "You have unsaved changes. Do you want to save them before closing?",
                    "Unsaved Changes",
                    MessageBoxButton.YesNoCancel,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    SaveChanges_Click(sender, e);
                }
                else if (result == MessageBoxResult.Cancel)
                {
                    return; // Don't close
                }
            }
            Close();
        }

        // Drag and Drop Event Handlers
        private void RulesDataGrid_PreviewMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            _startPoint = e.GetPosition(null);
            _isDragging = false;
        }

        private void RulesDataGrid_MouseMove(object sender, MouseEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed && !_isDragging)
            {
                Point mousePos = e.GetPosition(null);
                Vector diff = _startPoint - mousePos;

                if (Math.Abs(diff.X) > SystemParameters.MinimumHorizontalDragDistance ||
                    Math.Abs(diff.Y) > SystemParameters.MinimumVerticalDragDistance)
                {
                    // Get the dragged rule
                    var row = FindAncestor<DataGridRow>((DependencyObject)e.OriginalSource);
                    if (row != null)
                    {
                        _draggedRule = row.Item as ClassificationRule;
                        if (_draggedRule != null)
                        {
                            _isDragging = true;
                            DragDrop.DoDragDrop(RulesDataGrid, _draggedRule, DragDropEffects.Move);
                        }
                    }
                }
            }
        }

        private void RulesDataGrid_DragOver(object sender, DragEventArgs e)
        {
            e.Effects = DragDropEffects.Move;
        }

        private void RulesDataGrid_Drop(object sender, DragEventArgs e)
        {
            if (_draggedRule == null) return;

            var targetRow = FindAncestor<DataGridRow>((DependencyObject)e.OriginalSource);
            if (targetRow != null)
            {
                var targetRule = targetRow.Item as ClassificationRule;
                if (targetRule != null && targetRule != _draggedRule)
                {
                    // Reorder rules
                    int draggedIndex = _rules.IndexOf(_draggedRule);
                    int targetIndex = _rules.IndexOf(targetRule);

                    if (draggedIndex >= 0 && targetIndex >= 0)
                    {
                        _rules.Move(draggedIndex, targetIndex);
                        UpdatePrioritiesAfterReorder();
                        _hasUnsavedChanges = true;
                        UpdateButtonStates();
                    }
                }
            }

            _isDragging = false;
            _draggedRule = null;
        }

        private static T? FindAncestor<T>(DependencyObject current) where T : class
        {
            do
            {
                if (current is T ancestor)
                {
                    return ancestor;
                }
                current = System.Windows.Media.VisualTreeHelper.GetParent(current);
            }
            while (current != null);
            return null;
        }

        private void UpdatePrioritiesAfterReorder()
        {
            // Update priorities based on new order (higher index = higher priority)
            for (int i = 0; i < _rules.Count; i++)
            {
                _rules[i].Priority = _rules.Count - i;
            }
        }

        // Inline Editing Event Handlers
        private void RulesDataGrid_BeginningEdit(object sender, DataGridBeginningEditEventArgs e)
        {
            // Allow editing for specific columns only
            var columnHeader = e.Column.Header?.ToString();
            if (columnHeader == "Applied" || columnHeader == "Last Used" || columnHeader == "Confidence")
            {
                e.Cancel = true; // Prevent editing of read-only columns
            }
        }

        private void RulesDataGrid_CellEditEnding(object sender, DataGridCellEditEndingEventArgs e)
        {
            if (e.EditAction == DataGridEditAction.Commit)
            {
                _hasUnsavedChanges = true;
                UpdateButtonStates();
            }
        }

        // New Button Event Handlers
        private void SaveChanges_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Save all rules with updated priorities and inline edits
                _ruleManager.SaveRules();
                _hasUnsavedChanges = false;
                UpdateButtonStates();
                MessageBox.Show("Changes saved successfully!", "Save Complete",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving changes: {ex.Message}", "Save Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ResetOrder_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "This will reset the rule order to the original priority values. Continue?",
                "Reset Order",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                LoadRules(); // Reload from saved data
                _hasUnsavedChanges = false;
                UpdateButtonStates();
            }
        }

        private void DuplicateRule_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedRule == null) return;

            // Create a copy of the selected rule
            var duplicatedRule = new ClassificationRule
            {
                Name = $"{_selectedRule.Name} (Copy)",
                Comment = _selectedRule.Comment,
                Description = _selectedRule.Description,
                Priority = _selectedRule.Priority - 1, // Lower priority than original
                IsEnabled = _selectedRule.IsEnabled,
                LogicalOperator = _selectedRule.LogicalOperator,
                Conditions = _selectedRule.Conditions.Select(c => new RuleCondition
                {
                    ColumnName = c.ColumnName,
                    Operator = c.Operator,
                    Value = c.Value,
                    IsCaseSensitive = c.IsCaseSensitive
                }).ToList()
            };

            _ruleManager.AddRule(duplicatedRule);
            LoadRules();

            // Select the new rule
            RulesDataGrid.SelectedItem = _rules.FirstOrDefault(r => r.Id == duplicatedRule.Id);
        }
    }
}
