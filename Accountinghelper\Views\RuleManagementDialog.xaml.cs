using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using Accountinghelper.Models;
using Accountinghelper.Services;

namespace Accountinghelper.Views
{
    public partial class RuleManagementDialog : Window
    {
        private readonly RuleManager _ruleManager;
        private readonly List<BankTransaction>? _transactionData;
        private ObservableCollection<ClassificationRule> _rules;
        private ClassificationRule? _selectedRule;

        public RuleManagementDialog(RuleManager ruleManager, List<BankTransaction>? transactionData = null)
        {
            InitializeComponent();
            _ruleManager = ruleManager;
            _transactionData = transactionData;
            _rules = new ObservableCollection<ClassificationRule>();
            LoadRules();
        }

        private void LoadRules()
        {
            _rules.Clear();
            var rules = _ruleManager.GetRulesByPriority();
            foreach (var rule in rules)
            {
                _rules.Add(rule);
            }
            RulesDataGrid.ItemsSource = _rules;
        }

        private void RulesDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedRule = RulesDataGrid.SelectedItem as ClassificationRule;
            UpdateRuleDetails();
            UpdateButtonStates();
        }

        private void UpdateRuleDetails()
        {
            if (_selectedRule == null)
            {
                RuleNameText.Text = "No rule selected";
                RuleDescriptionText.Text = "";
                RuleConditionsText.Text = "";
                return;
            }

            RuleNameText.Text = $"Name: {_selectedRule.Name}";
            RuleDescriptionText.Text = $"Description: {_selectedRule.Description}";
            
            var conditionsText = "Conditions: ";
            if (_selectedRule.Conditions.Any())
            {
                var conditionStrings = _selectedRule.Conditions.Select(c => c.ToString());
                conditionsText += string.Join($" {_selectedRule.LogicalOperator} ", conditionStrings);
            }
            else
            {
                conditionsText += "No conditions defined";
            }
            
            RuleConditionsText.Text = conditionsText;
        }

        private void UpdateButtonStates()
        {
            bool hasSelection = _selectedRule != null;
            EditRuleButton.IsEnabled = hasSelection;
            DeleteRuleButton.IsEnabled = hasSelection;
            TestRuleButton.IsEnabled = hasSelection;
        }

        private void NewRule_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new RuleCreationDialog(_ruleManager, null, _transactionData);
            if (dialog.ShowDialog() == true)
            {
                LoadRules();
            }
        }

        private void EditRule_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedRule == null) return;

            var dialog = new RuleCreationDialog(_ruleManager, _selectedRule, _transactionData);
            if (dialog.ShowDialog() == true)
            {
                LoadRules();
            }
        }

        private void DeleteRule_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedRule == null) return;

            var result = MessageBox.Show(
                $"Are you sure you want to delete the rule '{_selectedRule.Name}'?",
                "Confirm Delete",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                _ruleManager.DeleteRule(_selectedRule.Id);
                LoadRules();
            }
        }

        private void TestRule_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedRule == null) return;

            var dialog = new RuleTestDialog(_selectedRule, _transactionData);
            dialog.Owner = this;
            dialog.ShowDialog();
        }

        private void ExportRules_Click(object sender, RoutedEventArgs e)
        {
            var saveFileDialog = new SaveFileDialog
            {
                Title = "Export Rules",
                Filter = "JSON Files (*.json)|*.json|All Files (*.*)|*.*",
                FilterIndex = 1,
                FileName = "rules.json"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    _ruleManager.ExportRules(saveFileDialog.FileName);
                    MessageBox.Show("Rules exported successfully!", "Export Complete", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error exporting rules: {ex.Message}", "Export Error", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void ImportRules_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "Import Rules",
                Filter = "JSON Files (*.json)|*.json|All Files (*.*)|*.*",
                FilterIndex = 1
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    _ruleManager.ImportRules(openFileDialog.FileName);
                    LoadRules();
                    MessageBox.Show("Rules imported successfully!", "Import Complete", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error importing rules: {ex.Message}", "Import Error", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
