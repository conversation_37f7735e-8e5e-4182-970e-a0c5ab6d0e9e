﻿<Window x:Class="Accountinghelper.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Accountinghelper"
        mc:Ignorable="d"
        Title="Bank Statement Classification Tool"
        Height="900" Width="1600"
        WindowState="Maximized"
        WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <!-- Styles for highlighting current row -->
        <Style x:Key="CurrentRowStyle" TargetType="DataGridRow">
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsCurrentRow}" Value="True">
                    <Setter Property="Background" Value="LightBlue"/>
                    <Setter Property="FontWeight" Value="Bold"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- Style for rule applied indicator -->
        <Style x:Key="RuleAppliedCellStyle" TargetType="DataGridCell">
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsRuleApplied}" Value="True">
                    <Setter Property="Background" Value="LightGreen"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- Menu -->
            <RowDefinition Height="Auto"/> <!-- Toolbar -->
            <RowDefinition Height="*"/>    <!-- Main Content -->
            <RowDefinition Height="Auto"/> <!-- Status Bar -->
        </Grid.RowDefinitions>

        <!-- Menu Bar -->
        <Menu Grid.Row="0">
            <MenuItem Header="_File">
                <MenuItem Header="_Open Bank Statement..." Click="OpenBankStatement_Click" InputGestureText="Ctrl+O"/>
                <MenuItem Header="_Save" Click="SaveFile_Click" InputGestureText="Ctrl+S"/>
                <MenuItem Header="Save _As..." Click="SaveAsFile_Click" InputGestureText="Ctrl+Shift+S"/>
                <Separator/>
                <MenuItem Header="_Import Rules..." Click="ImportRules_Click"/>
                <MenuItem Header="_Export Rules..." Click="ExportRules_Click"/>
                <Separator/>
                <MenuItem Header="E_xit" Click="Exit_Click" InputGestureText="Alt+F4"/>
            </MenuItem>
            <MenuItem Header="_Rules">
                <MenuItem Header="_Manage Rules..." Click="ManageRules_Click" InputGestureText="Ctrl+R"/>
                <MenuItem Header="_Create New Rule..." Click="CreateRule_Click" InputGestureText="Ctrl+N"/>
                <MenuItem Header="_Analyze Reference Files..." Click="AnalyzeReferences_Click"/>
                <Separator/>
                <MenuItem Header="_Apply All Rules" Click="ApplyAllRules_Click" InputGestureText="F5"/>
                <MenuItem Header="_Clear All Classifications" Click="ClearClassifications_Click"/>
            </MenuItem>
            <MenuItem Header="_Process">
                <MenuItem Header="_Start Processing" Click="StartProcessing_Click" InputGestureText="F2"/>
                <MenuItem Header="_Next Row" Click="NextRow_Click" InputGestureText="F3"/>
                <MenuItem Header="_Previous Row" Click="PreviousRow_Click" InputGestureText="F1"/>
                <MenuItem Header="_Skip Row" Click="SkipRow_Click" InputGestureText="F4"/>
            </MenuItem>
            <MenuItem Header="_Help">
                <MenuItem Header="_Keyboard Shortcuts" Click="ShowShortcuts_Click" InputGestureText="F1"/>
                <MenuItem Header="_About" Click="About_Click"/>
            </MenuItem>
        </Menu>

        <!-- Toolbar -->
        <ToolBar Grid.Row="1">
            <Button Name="OpenButton" Click="OpenBankStatement_Click" ToolTip="Open Bank Statement (Ctrl+O)">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="📁" FontSize="16" Margin="0,0,5,0"/>
                    <TextBlock Text="Open"/>
                </StackPanel>
            </Button>
            <Button Name="SaveButton" Click="SaveFile_Click" ToolTip="Save (Ctrl+S)" IsEnabled="False">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="💾" FontSize="16" Margin="0,0,5,0"/>
                    <TextBlock Text="Save"/>
                </StackPanel>
            </Button>
            <Separator/>
            <Button Name="CreateRuleButton" Click="CreateRule_Click" ToolTip="Create New Rule (Ctrl+N)" IsEnabled="False">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="➕" FontSize="16" Margin="0,0,5,0"/>
                    <TextBlock Text="New Rule"/>
                </StackPanel>
            </Button>
            <Button Name="ManageRulesButton" Click="ManageRules_Click" ToolTip="Manage Rules (Ctrl+R)">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="⚙️" FontSize="16" Margin="0,0,5,0"/>
                    <TextBlock Text="Manage Rules"/>
                </StackPanel>
            </Button>
            <Separator/>
            <Button Name="StartProcessingButton" Click="StartProcessing_Click" ToolTip="Start Processing (F2)" IsEnabled="False">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="▶️" FontSize="16" Margin="0,0,5,0"/>
                    <TextBlock Text="Start Processing"/>
                </StackPanel>
            </Button>
            <Button Name="ApplyAllRulesButton" Click="ApplyAllRules_Click" ToolTip="Apply All Rules (F5)" IsEnabled="False">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="🔄" FontSize="16" Margin="0,0,5,0"/>
                    <TextBlock Text="Apply All Rules"/>
                </StackPanel>
            </Button>
        </ToolBar>

        <!-- Main Content Area -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="400"/>
            </Grid.ColumnDefinitions>

            <!-- Bank Statement DataGrid -->
            <DataGrid Grid.Column="0"
                      Name="BankStatementDataGrid"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      SelectionMode="Single"
                      RowStyle="{StaticResource CurrentRowStyle}"
                      SelectionChanged="BankStatementDataGrid_SelectionChanged"
                      Margin="5">

                <DataGrid.Columns>
                    <!-- Column order matches Excel file: SN, TranId, ValueDate, TransactionDate, TransactionPostedDate, TransactionRemarks, WithdrawalAmtINR, DepositAmtINR, BalanceINR, MyComments -->
                    <DataGridTextColumn Header="SN" Binding="{Binding RowIndex}" Width="50" IsReadOnly="True"/>
                    <DataGridTextColumn Header="TranId" Binding="{Binding ReferenceNumber}" Width="100" IsReadOnly="True"/>
                    <DataGridTextColumn Header="ValueDate" Binding="{Binding ValueDate, StringFormat=dd/MM/yyyy}" Width="100" IsReadOnly="True"/>
                    <DataGridTextColumn Header="TransactionDate" Binding="{Binding TransactionDate, StringFormat=dd/MM/yyyy}" Width="120" IsReadOnly="True"/>
                    <DataGridTextColumn Header="TransactionPostedDate" Binding="{Binding TransactionPostedDate, StringFormat=dd/MM/yyyy HH:mm:ss}" Width="150" IsReadOnly="True"/>
                    <DataGridTextColumn Header="TransactionRemarks" Binding="{Binding TransactionRemarks}" Width="250" IsReadOnly="True"/>
                    <DataGridTextColumn Header="WithdrawalAmtINR" Binding="{Binding DebitAmount, StringFormat=N2}" Width="120" IsReadOnly="True"/>
                    <DataGridTextColumn Header="DepositAmtINR" Binding="{Binding CreditAmount, StringFormat=N2}" Width="120" IsReadOnly="True"/>
                    <DataGridTextColumn Header="BalanceINR" Binding="{Binding Balance, StringFormat=N2}" Width="120" IsReadOnly="True"/>
                    <DataGridTextColumn Header="MyComments"
                                        Binding="{Binding MyComments}"
                                        Width="200"
                                        CellStyle="{StaticResource RuleAppliedCellStyle}"/>
                </DataGrid.Columns>
            </DataGrid>

            <!-- Splitter -->
            <GridSplitter Grid.Column="1" Width="5" HorizontalAlignment="Center" VerticalAlignment="Stretch"/>

            <!-- Rule Creation/Management Panel -->
            <Grid Grid.Column="2" Margin="5">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Current Row Info -->
                <GroupBox Grid.Row="0" Header="Current Transaction" Margin="0,0,0,10">
                    <StackPanel>
                        <TextBlock Name="CurrentRowInfo" Text="No row selected" FontWeight="Bold" Margin="5"/>
                        <TextBlock Name="CurrentTransactionDetails" Text="" TextWrapping="Wrap" Margin="5,0,5,5"/>
                    </StackPanel>
                </GroupBox>

                <!-- Rule Creation Panel -->
                <TabControl Grid.Row="1">
                    <TabItem Header="Quick Actions &amp; Rule Creator">
                        <ScrollViewer>
                            <StackPanel Margin="10">
                                <!-- Quick Actions Section -->
                                <GroupBox Header="⚡ Quick Actions" Margin="0,0,0,20">
                                    <StackPanel Margin="10">
                                        <Button Name="ManualEntryButton"
                                                Content="Manual Entry"
                                                Click="ManualEntry_Click"
                                                Margin="0,5"
                                                Padding="10,5"
                                                IsEnabled="False"
                                                ToolTip="Manually enter a classification for the current transaction"/>

                                        <TextBox Name="ManualCommentTextBox"
                                                 Text=""
                                                 Margin="0,5"
                                                 Padding="5"
                                                 ToolTip="Enter the classification comment for the current transaction"/>

                                        <Button Name="ApplyManualCommentButton"
                                                Content="Apply Comment"
                                                Click="ApplyManualComment_Click"
                                                Margin="0,5"
                                                Padding="10,5"
                                                IsEnabled="False"
                                                ToolTip="Apply the entered comment to the current transaction"/>

                                        <Separator Margin="0,10"/>

                                        <TextBlock Text="Suggested Rules:" FontWeight="Bold" Margin="0,5"/>
                                        <ListBox Name="SuggestedRulesListBox"
                                                 Height="120"
                                                 SelectionChanged="SuggestedRulesListBox_SelectionChanged"
                                                 ToolTip="AI-suggested rules based on current transaction patterns"/>

                                        <Button Name="ApplySuggestedRuleButton"
                                                Content="Apply Selected Rule"
                                                Click="ApplySuggestedRule_Click"
                                                Margin="0,5"
                                                Padding="10,5"
                                                IsEnabled="False"
                                                ToolTip="Apply the selected suggested rule to the current transaction"/>
                                    </StackPanel>
                                </GroupBox>

                                <!-- Fast Transaction Rule Creator -->
                                <GroupBox Header="⚡ Fast Transaction Rule Creator" Margin="0,0,0,15">
                                    <StackPanel Margin="10">
                                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                            <TextBlock Text="Quick rule for transaction remarks"
                                                       FontWeight="Bold"
                                                       FontSize="14"
                                                       VerticalAlignment="Center"/>
                                            <Button Content="?"
                                                    Width="18"
                                                    Height="18"
                                                    Margin="8,0,0,0"
                                                    Background="Orange"
                                                    BorderThickness="1"
                                                    BorderBrush="DarkOrange"
                                                    FontSize="10"
                                                    FontWeight="Bold"
                                                    Cursor="Hand"
                                                    ToolTip="Create rules quickly for transaction remarks. Most rules follow the pattern 'TransactionRemarks contains [value]'. Use commas to create multiple rules at once."/>
                                        </StackPanel>

                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <TextBlock Grid.Column="0"
                                                       Text="TransactionRemarks contains:"
                                                       FontWeight="Bold"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,10,0"/>

                                            <TextBox Grid.Column="1"
                                                     Name="FastRuleValueTextBox"
                                                     FontSize="14"
                                                     Padding="8"
                                                     ToolTip="Enter search terms (e.g., 'SALARY', 'ATM', 'TRANSFER'). Use commas to create multiple rules: 'SALARY, BONUS, INCOME'"/>

                                            <Button Grid.Column="2"
                                                    Name="CreateFastRuleButton"
                                                    Content="✨ Create"
                                                    Margin="10,0,0,0"
                                                    Padding="15,8"
                                                    Background="Gold"
                                                    FontWeight="Bold"
                                                    Click="CreateFastRule_Click"
                                                    ToolTip="Create rule(s) instantly"/>
                                        </Grid>

                                        <!-- Template shortcuts -->
                                        <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                                            <TextBlock Text="Quick templates:" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                            <Button Content="SALARY" Click="FastRuleTemplate_Click" Tag="SALARY" Margin="2" Padding="5,2" Background="LightBlue"/>
                                            <Button Content="ATM" Click="FastRuleTemplate_Click" Tag="ATM" Margin="2" Padding="5,2" Background="LightBlue"/>
                                            <Button Content="TRANSFER" Click="FastRuleTemplate_Click" Tag="TRANSFER" Margin="2" Padding="5,2" Background="LightBlue"/>
                                            <Button Content="UPI" Click="FastRuleTemplate_Click" Tag="UPI" Margin="2" Padding="5,2" Background="LightBlue"/>
                                            <Button Content="NEFT" Click="FastRuleTemplate_Click" Tag="NEFT" Margin="2" Padding="5,2" Background="LightBlue"/>
                                        </StackPanel>
                                    </StackPanel>
                                </GroupBox>

                                <!-- Rule Creator Section -->
                                <GroupBox Header="🔧 Advanced Rule Creator" Margin="0,0,0,15">
                                    <StackPanel Margin="10">
                                        <!-- Header with help icon -->
                                        <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                            <TextBlock Text="Create a rule quickly and easily"
                                                       FontWeight="Bold"
                                                       FontSize="14"
                                                       VerticalAlignment="Center"/>
                                            <Button Content="ⓘ"
                                                    Width="20"
                                                    Height="20"
                                                    Margin="10,0,0,0"
                                                    Background="Transparent"
                                                    BorderThickness="1"
                                                    BorderBrush="LightGray"
                                                    FontSize="12"
                                                    Cursor="Hand"
                                                    ToolTip="Simply specify what to look for and how to classify matching transactions. Use natural language like 'transactions containing SALARY' or 'deposit greater than 1000'"/>
                                        </StackPanel>

                                        <!-- Conditions Input Section -->
                                        <GroupBox Header="🎯 What to look for" Margin="0,0,0,15">
                                            <StackPanel Margin="10">
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                                    <TextBlock Text="Enter conditions (one per line):" FontWeight="Bold" VerticalAlignment="Center"/>
                                                    <Button Content="?"
                                                            Width="18"
                                                            Height="18"
                                                            Margin="8,0,0,0"
                                                            Background="LightBlue"
                                                            BorderThickness="1"
                                                            BorderBrush="DarkBlue"
                                                            FontSize="10"
                                                            FontWeight="Bold"
                                                            Cursor="Hand"
                                                            ToolTip="Examples:&#x0a;• 'transactions containing MaheshSalary'&#x0a;• 'deposit greater than 1000'&#x0a;• 'withdrawal greater than 500'&#x0a;• 'description contains SALARY'&#x0a;• 'balance less than 10000'&#x0a;&#x0a;Multiple conditions = AND logic"/>
                                                </StackPanel>
                                                <TextBox Name="QuickRuleConditionsTextBox"
                                                         Height="80"
                                                         TextWrapping="Wrap"
                                                         AcceptsReturn="True"
                                                         VerticalScrollBarVisibility="Auto"
                                                         Margin="0,0,0,10"
                                                         ToolTip="Enter conditions that transactions must match. Use natural language patterns like 'contains', 'greater than', 'starts with', etc."/>
                                            </StackPanel>
                                        </GroupBox>

                                        <!-- Classification Input Section -->
                                        <GroupBox Header="📝 How to classify" Margin="0,0,0,15">
                                            <StackPanel Margin="10">
                                                <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                                    <TextBlock Text="What should matching transactions be classified as:" FontWeight="Bold" VerticalAlignment="Center"/>
                                                    <Button Content="?"
                                                            Width="18"
                                                            Height="18"
                                                            Margin="8,0,0,0"
                                                            Background="LightGreen"
                                                            BorderThickness="1"
                                                            BorderBrush="DarkGreen"
                                                            FontSize="10"
                                                            FontWeight="Bold"
                                                            Cursor="Hand"
                                                            ToolTip="Examples:&#x0a;• 'Mahesh Salary'&#x0a;• 'Large Expense'&#x0a;• 'ATM Withdrawal'&#x0a;• 'Income'&#x0a;• 'Bank Transfer'&#x0a;• 'Utility Payment'"/>
                                                </StackPanel>
                                                <TextBox Name="QuickRuleClassificationTextBox"
                                                         Margin="0,0,0,10"
                                                         ToolTip="Enter what matching transactions should be classified as. This will appear in the MyComments column."/>
                                            </StackPanel>
                                        </GroupBox>

                                        <!-- Examples -->
                                        <Expander Header="💡 Examples &amp; Tips" Margin="0,0,0,10" IsExpanded="False">
                                            <StackPanel Margin="10,5">
                                                <TextBlock Text="✅ Example 1:" FontWeight="Bold" Margin="0,0,0,5"/>
                                                <TextBlock Text="Conditions: 'transactions containing MaheshSalary'" Margin="10,2" Foreground="DarkBlue"/>
                                                <TextBlock Text="Classification: 'Mahesh Salary'" Margin="10,2" Foreground="DarkGreen"/>

                                                <TextBlock Text="✅ Example 2:" FontWeight="Bold" Margin="0,10,0,5"/>
                                                <TextBlock Text="Conditions: 'deposit greater than 1000'" Margin="10,2" Foreground="DarkBlue"/>
                                                <TextBlock Text="Classification: 'Large Deposit'" Margin="10,2" Foreground="DarkGreen"/>

                                                <TextBlock Text="✅ Example 3:" FontWeight="Bold" Margin="0,10,0,5"/>
                                                <TextBlock Text="Conditions: 'withdrawal greater than 500'" Margin="10,2" Foreground="DarkBlue"/>
                                                <TextBlock Text="Classification: 'Large Withdrawal'" Margin="10,2" Foreground="DarkGreen"/>

                                                <TextBlock Text="💡 Tips:" FontWeight="Bold" Margin="0,10,0,5"/>
                                                <TextBlock Text="• Use 'contains', 'starts with', 'ends with' for text matching" Margin="10,2"/>
                                                <TextBlock Text="• Use 'deposit greater than', 'withdrawal greater than' for amounts" Margin="10,2"/>
                                                <TextBlock Text="• Multiple conditions will be combined with AND logic" Margin="10,2"/>
                                                <TextBlock Text="• Spaces in values will be automatically handled" Margin="10,2"/>
                                                <TextBlock Text="• Boolean operators: Only implicit AND (multiple lines)" Margin="10,2"/>
                                            </StackPanel>
                                        </Expander>

                                        <!-- Action Buttons -->
                                        <StackPanel Orientation="Horizontal" Margin="0,10">
                                            <Button Name="CreateAdvancedRuleButton"
                                                    Content="🔧 Advanced Rule Builder"
                                                    Click="CreateRule_Click"
                                                    Margin="0,0,10,0"
                                                    Padding="15,8"
                                                    Background="LightBlue"
                                                    ToolTip="Open the full rule creation dialog with visual builder and advanced options"/>
                                            <Button Name="QuickCreateRuleButton"
                                                    Content="✨ Create Rule"
                                                    Click="QuickCreateRule_Click"
                                                    Padding="15,8"
                                                    Background="LightGreen"
                                                    ToolTip="Create a rule from the conditions and classification above"/>
                                        </StackPanel>

                                        <!-- Status/Feedback -->
                                        <TextBlock Name="QuickRuleStatusText"
                                                   Margin="0,10,0,0"
                                                   TextWrapping="Wrap"
                                                   Visibility="Collapsed"/>
                                    </StackPanel>
                                </GroupBox>
                            </StackPanel>
                        </ScrollViewer>
                    </TabItem>
                </TabControl>

                <!-- Processing Controls -->
                <GroupBox Grid.Row="2" Header="Processing Controls" Margin="0,10,0,0">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="5">
                            <Button Name="PreviousRowButton"
                                    Content="◀ Previous"
                                    Click="PreviousRow_Click"
                                    Margin="0,0,5,0"
                                    Padding="10,5"
                                    IsEnabled="False"/>
                            <Button Name="NextRowButton"
                                    Content="Next ▶"
                                    Click="NextRow_Click"
                                    Margin="5,0,0,0"
                                    Padding="10,5"
                                    IsEnabled="False"/>
                        </StackPanel>

                        <StackPanel Orientation="Horizontal" Margin="5">
                            <Button Name="SkipRowButton"
                                    Content="Skip Row"
                                    Click="SkipRow_Click"
                                    Margin="0,0,5,0"
                                    Padding="10,5"
                                    IsEnabled="False"/>
                            <Button Name="UndoButton"
                                    Content="Undo"
                                    Click="Undo_Click"
                                    Margin="5,0,0,0"
                                    Padding="10,5"
                                    IsEnabled="False"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>
            </Grid>
        </Grid>

        <!-- Status Bar -->
        <StatusBar Grid.Row="3">
            <StatusBarItem>
                <TextBlock Name="StatusText" Text="Ready"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Name="ProgressText" Text=""/>
                    <ProgressBar Name="ProgressBar" Width="200" Height="16" Margin="10,0,0,0" Visibility="Collapsed"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>

        <!-- Toast Notification Container (positioned above everything) -->
        <Canvas Name="ToastContainer"
                Grid.RowSpan="4"
                IsHitTestVisible="False"
                Panel.ZIndex="1000"/>
    </Grid>
</Window>
