﻿<Window x:Class="Accountinghelper.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Accountinghelper"
        mc:Ignorable="d"
        Title="Bank Statement Classification Tool"
        Height="900" Width="1600"
        WindowState="Maximized"
        WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <!-- Styles for highlighting current row -->
        <Style x:Key="CurrentRowStyle" TargetType="DataGridRow">
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsCurrentRow}" Value="True">
                    <Setter Property="Background" Value="LightBlue"/>
                    <Setter Property="FontWeight" Value="Bold"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- Style for rule applied indicator -->
        <Style x:Key="RuleAppliedCellStyle" TargetType="DataGridCell">
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsRuleApplied}" Value="True">
                    <Setter Property="Background" Value="LightGreen"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- Menu -->
            <RowDefinition Height="Auto"/> <!-- Toolbar -->
            <RowDefinition Height="*"/>    <!-- Main Content -->
            <RowDefinition Height="Auto"/> <!-- Status Bar -->
        </Grid.RowDefinitions>

        <!-- Menu Bar -->
        <Menu Grid.Row="0">
            <MenuItem Header="_File">
                <MenuItem Header="_Open Bank Statement..." Click="OpenBankStatement_Click" InputGestureText="Ctrl+O"/>
                <MenuItem Header="_Save" Click="SaveFile_Click" InputGestureText="Ctrl+S"/>
                <MenuItem Header="Save _As..." Click="SaveAsFile_Click" InputGestureText="Ctrl+Shift+S"/>
                <Separator/>
                <MenuItem Header="_Import Rules..." Click="ImportRules_Click"/>
                <MenuItem Header="_Export Rules..." Click="ExportRules_Click"/>
                <Separator/>
                <MenuItem Header="E_xit" Click="Exit_Click" InputGestureText="Alt+F4"/>
            </MenuItem>
            <MenuItem Header="_Rules">
                <MenuItem Header="_Manage Rules..." Click="ManageRules_Click" InputGestureText="Ctrl+R"/>
                <MenuItem Header="_Create New Rule..." Click="CreateRule_Click" InputGestureText="Ctrl+N"/>
                <MenuItem Header="_Analyze Reference Files..." Click="AnalyzeReferences_Click"/>
                <Separator/>
                <MenuItem Header="_Apply All Rules" Click="ApplyAllRules_Click" InputGestureText="F5"/>
                <MenuItem Header="_Clear All Classifications" Click="ClearClassifications_Click"/>
            </MenuItem>
            <MenuItem Header="_Process">
                <MenuItem Header="_Start Processing" Click="StartProcessing_Click" InputGestureText="F2"/>
                <MenuItem Header="_Next Row" Click="NextRow_Click" InputGestureText="F3"/>
                <MenuItem Header="_Previous Row" Click="PreviousRow_Click" InputGestureText="F1"/>
                <MenuItem Header="_Skip Row" Click="SkipRow_Click" InputGestureText="F4"/>
            </MenuItem>
            <MenuItem Header="_Help">
                <MenuItem Header="_Keyboard Shortcuts" Click="ShowShortcuts_Click" InputGestureText="F1"/>
                <MenuItem Header="_About" Click="About_Click"/>
            </MenuItem>
        </Menu>

        <!-- Toolbar -->
        <ToolBar Grid.Row="1">
            <Button Name="OpenButton" Click="OpenBankStatement_Click" ToolTip="Open Bank Statement (Ctrl+O)">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="📁" FontSize="16" Margin="0,0,5,0"/>
                    <TextBlock Text="Open"/>
                </StackPanel>
            </Button>
            <Button Name="SaveButton" Click="SaveFile_Click" ToolTip="Save (Ctrl+S)" IsEnabled="False">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="💾" FontSize="16" Margin="0,0,5,0"/>
                    <TextBlock Text="Save"/>
                </StackPanel>
            </Button>
            <Separator/>
            <Button Name="CreateRuleButton" Click="CreateRule_Click" ToolTip="Create New Rule (Ctrl+N)" IsEnabled="False">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="➕" FontSize="16" Margin="0,0,5,0"/>
                    <TextBlock Text="New Rule"/>
                </StackPanel>
            </Button>
            <Button Name="ManageRulesButton" Click="ManageRules_Click" ToolTip="Manage Rules (Ctrl+R)">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="⚙️" FontSize="16" Margin="0,0,5,0"/>
                    <TextBlock Text="Manage Rules"/>
                </StackPanel>
            </Button>
            <Separator/>
            <Button Name="StartProcessingButton" Click="StartProcessing_Click" ToolTip="Start Processing (F2)" IsEnabled="False">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="▶️" FontSize="16" Margin="0,0,5,0"/>
                    <TextBlock Text="Start Processing"/>
                </StackPanel>
            </Button>
            <Button Name="ApplyAllRulesButton" Click="ApplyAllRules_Click" ToolTip="Apply All Rules (F5)" IsEnabled="False">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="🔄" FontSize="16" Margin="0,0,5,0"/>
                    <TextBlock Text="Apply All Rules"/>
                </StackPanel>
            </Button>
        </ToolBar>

        <!-- Main Content Area -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="400"/>
            </Grid.ColumnDefinitions>

            <!-- Bank Statement DataGrid -->
            <DataGrid Grid.Column="0"
                      Name="BankStatementDataGrid"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      SelectionMode="Single"
                      RowStyle="{StaticResource CurrentRowStyle}"
                      SelectionChanged="BankStatementDataGrid_SelectionChanged"
                      Margin="5">

                <DataGrid.Columns>
                    <!-- Column order matches Excel file: SN, TranId, ValueDate, TransactionDate, TransactionPostedDate, TransactionRemarks, WithdrawalAmtINR, DepositAmtINR, BalanceINR, MyComments -->
                    <DataGridTextColumn Header="SN" Binding="{Binding RowIndex}" Width="50" IsReadOnly="True"/>
                    <DataGridTextColumn Header="TranId" Binding="{Binding ReferenceNumber}" Width="100" IsReadOnly="True"/>
                    <DataGridTextColumn Header="ValueDate" Binding="{Binding ValueDate, StringFormat=dd/MM/yyyy}" Width="100" IsReadOnly="True"/>
                    <DataGridTextColumn Header="TransactionDate" Binding="{Binding TransactionDate, StringFormat=dd/MM/yyyy}" Width="120" IsReadOnly="True"/>
                    <DataGridTextColumn Header="TransactionPostedDate" Binding="{Binding TransactionPostedDate, StringFormat=dd/MM/yyyy HH:mm:ss}" Width="150" IsReadOnly="True"/>
                    <DataGridTextColumn Header="TransactionRemarks" Binding="{Binding TransactionRemarks}" Width="250" IsReadOnly="True"/>
                    <DataGridTextColumn Header="WithdrawalAmtINR" Binding="{Binding DebitAmount, StringFormat=N2}" Width="120" IsReadOnly="True"/>
                    <DataGridTextColumn Header="DepositAmtINR" Binding="{Binding CreditAmount, StringFormat=N2}" Width="120" IsReadOnly="True"/>
                    <DataGridTextColumn Header="BalanceINR" Binding="{Binding Balance, StringFormat=N2}" Width="120" IsReadOnly="True"/>
                    <DataGridTextColumn Header="MyComments"
                                        Binding="{Binding MyComments}"
                                        Width="200"
                                        CellStyle="{StaticResource RuleAppliedCellStyle}"/>
                </DataGrid.Columns>
            </DataGrid>

            <!-- Splitter -->
            <GridSplitter Grid.Column="1" Width="5" HorizontalAlignment="Center" VerticalAlignment="Stretch"/>

            <!-- Rule Creation/Management Panel -->
            <Grid Grid.Column="2" Margin="5">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Current Row Info -->
                <GroupBox Grid.Row="0" Header="Current Transaction" Margin="0,0,0,10">
                    <StackPanel>
                        <TextBlock Name="CurrentRowInfo" Text="No row selected" FontWeight="Bold" Margin="5"/>
                        <TextBlock Name="CurrentTransactionDetails" Text="" TextWrapping="Wrap" Margin="5,0,5,5"/>
                    </StackPanel>
                </GroupBox>

                <!-- Rule Creation Panel -->
                <TabControl Grid.Row="1">
                    <TabItem Header="Quick Actions">
                        <StackPanel Margin="10">
                            <Button Name="ManualEntryButton"
                                    Content="Manual Entry"
                                    Click="ManualEntry_Click"
                                    Margin="0,5"
                                    Padding="10,5"
                                    IsEnabled="False"/>

                            <TextBox Name="ManualCommentTextBox"
                                     Text=""
                                     Margin="0,5"
                                     Padding="5"/>

                            <Button Name="ApplyManualCommentButton"
                                    Content="Apply Comment"
                                    Click="ApplyManualComment_Click"
                                    Margin="0,5"
                                    Padding="10,5"
                                    IsEnabled="False"/>

                            <Separator Margin="0,10"/>

                            <TextBlock Text="Suggested Rules:" FontWeight="Bold" Margin="0,5"/>
                            <ListBox Name="SuggestedRulesListBox"
                                     Height="150"
                                     SelectionChanged="SuggestedRulesListBox_SelectionChanged"/>

                            <Button Name="ApplySuggestedRuleButton"
                                    Content="Apply Selected Rule"
                                    Click="ApplySuggestedRule_Click"
                                    Margin="0,5"
                                    Padding="10,5"
                                    IsEnabled="False"/>
                        </StackPanel>
                    </TabItem>

                    <TabItem Header="Quick Rule Creator">
                        <ScrollViewer>
                            <StackPanel Margin="10">
                                <!-- Header with helpful description -->
                                <TextBlock Text="Create a rule quickly and easily"
                                           FontWeight="Bold"
                                           FontSize="14"
                                           Margin="0,0,0,5"/>
                                <TextBlock Text="Simply specify what to look for and how to classify matching transactions"
                                           FontStyle="Italic"
                                           Foreground="Gray"
                                           Margin="0,0,0,15"/>

                                <!-- Conditions Input Section -->
                                <GroupBox Header="🎯 What to look for" Margin="0,0,0,15">
                                    <StackPanel Margin="10">
                                        <TextBlock Text="Enter conditions (one per line):" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <TextBlock Text="Examples: 'transactions containing MaheshSalary', 'amount greater than 1000', 'description contains SALARY'"
                                                   FontStyle="Italic"
                                                   Foreground="Gray"
                                                   TextWrapping="Wrap"
                                                   Margin="0,0,0,5"/>
                                        <TextBox Name="QuickRuleConditionsTextBox"
                                                 Height="80"
                                                 TextWrapping="Wrap"
                                                 AcceptsReturn="True"
                                                 VerticalScrollBarVisibility="Auto"
                                                 Margin="0,0,0,10"
                                                 ToolTip="Enter conditions that transactions must match:&#x0a;• 'transactions containing MaheshSalary'&#x0a;• 'amount greater than 1000'&#x0a;• 'description contains SALARY'&#x0a;• 'reference starts with UPI'&#x0a;&#x0a;You can enter multiple conditions, one per line."/>
                                    </StackPanel>
                                </GroupBox>

                                <!-- Classification Input Section -->
                                <GroupBox Header="📝 How to classify" Margin="0,0,0,15">
                                    <StackPanel Margin="10">
                                        <TextBlock Text="What should matching transactions be classified as:" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <TextBlock Text="Examples: 'Mahesh Salary', 'Large Expense', 'ATM Withdrawal', 'Income'"
                                                   FontStyle="Italic"
                                                   Foreground="Gray"
                                                   Margin="0,0,0,5"/>
                                        <TextBox Name="QuickRuleClassificationTextBox"
                                                 Margin="0,0,0,10"
                                                 ToolTip="Enter what matching transactions should be classified as (e.g., 'Mahesh Salary', 'Large Expense', 'ATM Withdrawal')"/>
                                    </StackPanel>
                                </GroupBox>

                                <!-- Examples -->
                                <Expander Header="💡 Examples &amp; Tips" Margin="0,0,0,10" IsExpanded="False">
                                    <StackPanel Margin="10,5">
                                        <TextBlock Text="✅ Example 1:" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <TextBlock Text="Conditions: 'transactions containing MaheshSalary'" Margin="10,2" Foreground="DarkBlue"/>
                                        <TextBlock Text="Classification: 'Mahesh Salary'" Margin="10,2" Foreground="DarkGreen"/>

                                        <TextBlock Text="✅ Example 2:" FontWeight="Bold" Margin="0,10,0,5"/>
                                        <TextBlock Text="Conditions: 'amount greater than 1000'" Margin="10,2" Foreground="DarkBlue"/>
                                        <TextBlock Text="Classification: 'Large Expense'" Margin="10,2" Foreground="DarkGreen"/>

                                        <TextBlock Text="💡 Tips:" FontWeight="Bold" Margin="0,10,0,5"/>
                                        <TextBlock Text="• Use 'contains', 'starts with', 'ends with' for text matching" Margin="10,2"/>
                                        <TextBlock Text="• Use 'greater than', 'less than', '&gt;', '&lt;' for amounts" Margin="10,2"/>
                                        <TextBlock Text="• Multiple conditions will be combined with AND logic" Margin="10,2"/>
                                        <TextBlock Text="• Spaces in values will be automatically handled" Margin="10,2"/>
                                    </StackPanel>
                                </Expander>

                                <!-- Action Buttons -->
                                <StackPanel Orientation="Horizontal" Margin="0,10">
                                    <Button Name="CreateAdvancedRuleButton"
                                            Content="🔧 Advanced Rule Builder"
                                            Click="CreateRule_Click"
                                            Margin="0,0,10,0"
                                            Padding="15,8"
                                            Background="LightBlue"
                                            ToolTip="Open the full rule creation dialog with visual builder and advanced options"/>
                                    <Button Name="QuickCreateRuleButton"
                                            Content="✨ Create Rule"
                                            Click="QuickCreateRule_Click"
                                            Padding="15,8"
                                            Background="LightGreen"
                                            ToolTip="Create a rule from the conditions and classification above"/>
                                </StackPanel>

                                <!-- Status/Feedback -->
                                <TextBlock Name="QuickRuleStatusText"
                                           Margin="0,10,0,0"
                                           TextWrapping="Wrap"
                                           Visibility="Collapsed"/>

                                <!-- Separator -->
                                <Separator Margin="0,15"/>

                                <!-- Quick Templates -->
                                <GroupBox Header="🚀 Quick Templates" Margin="0,0,0,10">
                                    <StackPanel Margin="10">
                                        <TextBlock Text="Click to instantly fill in common rule patterns:"
                                                   FontStyle="Italic"
                                                   Foreground="Gray"
                                                   Margin="0,0,0,10"/>

                                        <WrapPanel>
                                            <Button Name="SalaryTemplateButton"
                                                    Content="💰 Salary"
                                                    Margin="0,0,5,5"
                                                    Padding="10,5"
                                                    Click="SalaryTemplate_Click"
                                                    ToolTip="Fill in template for salary transactions"/>
                                            <Button Name="ATMTemplateButton"
                                                    Content="🏧 ATM"
                                                    Margin="0,0,5,5"
                                                    Padding="10,5"
                                                    Click="ATMTemplate_Click"
                                                    ToolTip="Fill in template for ATM withdrawals"/>
                                            <Button Name="TransferTemplateButton"
                                                    Content="🔄 Transfer"
                                                    Margin="0,0,5,5"
                                                    Padding="10,5"
                                                    Click="TransferTemplate_Click"
                                                    ToolTip="Fill in template for bank transfers"/>
                                            <Button Name="ExpenseTemplateButton"
                                                    Content="💳 Large Expense"
                                                    Margin="0,0,5,5"
                                                    Padding="10,5"
                                                    Click="ExpenseTemplate_Click"
                                                    ToolTip="Fill in template for large expense transactions"/>
                                        </WrapPanel>
                                    </StackPanel>
                                </GroupBox>
                            </StackPanel>
                        </ScrollViewer>
                    </TabItem>
                </TabControl>

                <!-- Processing Controls -->
                <GroupBox Grid.Row="2" Header="Processing Controls" Margin="0,10,0,0">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="5">
                            <Button Name="PreviousRowButton"
                                    Content="◀ Previous"
                                    Click="PreviousRow_Click"
                                    Margin="0,0,5,0"
                                    Padding="10,5"
                                    IsEnabled="False"/>
                            <Button Name="NextRowButton"
                                    Content="Next ▶"
                                    Click="NextRow_Click"
                                    Margin="5,0,0,0"
                                    Padding="10,5"
                                    IsEnabled="False"/>
                        </StackPanel>

                        <StackPanel Orientation="Horizontal" Margin="5">
                            <Button Name="SkipRowButton"
                                    Content="Skip Row"
                                    Click="SkipRow_Click"
                                    Margin="0,0,5,0"
                                    Padding="10,5"
                                    IsEnabled="False"/>
                            <Button Name="UndoButton"
                                    Content="Undo"
                                    Click="Undo_Click"
                                    Margin="5,0,0,0"
                                    Padding="10,5"
                                    IsEnabled="False"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>
            </Grid>
        </Grid>

        <!-- Status Bar -->
        <StatusBar Grid.Row="3">
            <StatusBarItem>
                <TextBlock Name="StatusText" Text="Ready"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Name="ProgressText" Text=""/>
                    <ProgressBar Name="ProgressBar" Width="200" Height="16" Margin="10,0,0,0" Visibility="Collapsed"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
