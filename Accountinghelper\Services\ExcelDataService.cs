using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using ClosedXML.Excel;
using Accountinghelper.Models;

namespace Accountinghelper.Services
{
    public class ExcelDataService : IDisposable
    {
        private XLWorkbook? _workbook;
        private IXLWorksheet? _worksheet;
        private bool _disposed = false;

        public List<string> ColumnNames { get; private set; } = new();
        public string FilePath { get; private set; } = string.Empty;

        public List<BankTransaction> LoadExcelFile(string filePath)
        {
            try
            {
                FilePath = filePath;

                // Check if file exists
                if (!File.Exists(filePath))
                    throw new FileNotFoundException($"File not found: {filePath}");

                // Validate file extension - only support .xlsx
                var extension = Path.GetExtension(filePath).ToLowerInvariant();
                if (extension != ".xlsx")
                    throw new InvalidOperationException($"Unsupported file format: {extension}. Please use .xlsx files only.");

                // Load workbook using ClosedXML
                try
                {
                    _workbook = new XLWorkbook(filePath);
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException(
                        $"Unable to open Excel file. The file may be corrupted, password-protected, or in an unsupported format. " +
                        $"Error: {ex.Message}", ex);
                }

                // Get the first worksheet
                _worksheet = _workbook.Worksheets.FirstOrDefault();
                if (_worksheet == null)
                    throw new InvalidOperationException("The Excel file contains no worksheets.");

                var transactions = new List<BankTransaction>();

                // Get the used range
                var usedRange = _worksheet.RangeUsed();
                if (usedRange == null)
                    return transactions; // Empty worksheet

                int rowCount = usedRange.RowCount();
                int colCount = usedRange.ColumnCount();

                if (rowCount < 2) // Need at least header + 1 data row
                    return transactions;

                // Read column headers from first row
                ColumnNames.Clear();
                for (int col = 1; col <= colCount; col++)
                {
                    var headerCell = _worksheet.Cell(1, col);
                    var headerValue = headerCell.GetString();
                    if (string.IsNullOrWhiteSpace(headerValue))
                        headerValue = $"Column{col}";
                    ColumnNames.Add(headerValue);
                }



                // Read data rows
                for (int row = 2; row <= rowCount; row++)
                {
                    var transaction = new BankTransaction
                    {
                        RowIndex = row - 1 // 0-based index for data rows
                    };

                    for (int col = 1; col <= colCount; col++)
                    {
                        var cell = _worksheet.Cell(row, col);
                        var cellValue = GetCellValue(cell);
                        var columnName = ColumnNames[col - 1];

                        MapCellValueToTransaction(transaction, columnName, cellValue);
                    }

                    transactions.Add(transaction);
                }

                return transactions;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error loading Excel file: {ex.Message}", ex);
            }
        }

        private object? GetCellValue(IXLCell cell)
        {
            if (cell.IsEmpty())
                return null;

            try
            {
                // Handle different data types
                if (cell.DataType == XLDataType.DateTime)
                    return cell.GetDateTime();
                else if (cell.DataType == XLDataType.Number)
                    return cell.GetDouble();
                else if (cell.DataType == XLDataType.Boolean)
                    return cell.GetBoolean();
                else
                    return cell.GetString();
            }
            catch
            {
                // Fallback to string value if type conversion fails
                try
                {
                    return cell.GetString();
                }
                catch
                {
                    return cell.Value.ToString();
                }
            }
        }

        private void MapCellValueToTransaction(BankTransaction transaction, string columnName, object? cellValue)
        {
            var normalizedColumnName = columnName.Replace(" ", "").ToLowerInvariant();

            switch (normalizedColumnName)
            {
                // Date fields - your file has ValueDate, TransactionDate, and TransactionPostedDate
                case "date":
                case "txndate":
                    if (cellValue != null && DateTime.TryParse(cellValue.ToString(), out var date))
                        transaction.Date = date;
                    break;

                case "valuedate":
                    if (cellValue != null && DateTime.TryParse(cellValue.ToString(), out var valueDate))
                    {
                        transaction.ValueDate = valueDate;
                        // Also set the main Date property if not already set
                        if (transaction.Date == null)
                            transaction.Date = valueDate;
                    }
                    break;

                case "transactiondate":
                    if (cellValue != null && DateTime.TryParse(cellValue.ToString(), out var transactionDate))
                    {
                        transaction.TransactionDate = transactionDate;
                        // Also set the main Date property if not already set
                        if (transaction.Date == null)
                            transaction.Date = transactionDate;
                    }
                    break;

                case "transactionposteddate":
                    if (cellValue != null && DateTime.TryParse(cellValue.ToString(), out var transactionPostedDate))
                    {
                        transaction.TransactionPostedDate = transactionPostedDate;
                    }
                    break;

                // Transaction remarks
                case "transactionremarks":
                case "remarks":
                case "narration":
                case "particulars":
                case "txnremarks":
                    transaction.TransactionRemarks = cellValue?.ToString() ?? string.Empty;
                    break;

                // Description (not present in your file, but keeping for compatibility)
                case "description":
                case "desc":
                case "txndescription":
                    transaction.Description = cellValue?.ToString() ?? string.Empty;
                    break;

                // Amount (general amount field)
                case "amount":
                case "txnamount":
                case "transactionamount":
                    if (cellValue != null && decimal.TryParse(cellValue.ToString(), out var amount))
                        transaction.Amount = amount;
                    break;

                // Debit/Withdrawal amounts - your file has WithdrawalAmtINR
                case "debit":
                case "debitamount":
                case "withdrawal":
                case "withdrawalamtinr":
                case "withdrawalamt":
                case "dr":
                case "debitamt":
                    if (cellValue != null && decimal.TryParse(cellValue.ToString(), out var debit) && debit > 0)
                        transaction.DebitAmount = debit;
                    break;

                // Credit/Deposit amounts - your file has DepositAmtINR
                case "credit":
                case "creditamount":
                case "deposit":
                case "depositamtinr":
                case "depositamt":
                case "cr":
                case "creditamt":
                    if (cellValue != null && decimal.TryParse(cellValue.ToString(), out var credit) && credit > 0)
                        transaction.CreditAmount = credit;
                    break;

                // Balance - your file has BalanceINR
                case "balance":
                case "balanceinr":
                case "runningbalance":
                case "closingbalance":
                case "bal":
                    if (cellValue != null && decimal.TryParse(cellValue.ToString(), out var balance))
                        transaction.Balance = balance;
                    break;

                // Reference/Transaction ID - your file has TranId
                case "reference":
                case "referencenumber":
                case "refno":
                case "chequeno":
                case "transactionid":
                case "tranid":
                case "txnid":
                case "ref":
                    transaction.ReferenceNumber = cellValue?.ToString() ?? string.Empty;
                    break;

                // Serial number
                case "sn":
                case "serialnumber":
                case "srno":
                    // Store as additional info, not used in main transaction fields
                    transaction.AdditionalColumns[columnName] = cellValue ?? string.Empty;
                    break;

                case "transactiontype":
                case "type":
                case "txntype":
                    transaction.TransactionType = cellValue?.ToString() ?? string.Empty;
                    break;

                case "branch":
                case "branchname":
                case "branchcode":
                    transaction.Branch = cellValue?.ToString() ?? string.Empty;
                    break;

                // Comments - your file has MyComments
                case "mycomments":
                case "comments":
                case "classification":
                case "notes":
                    transaction.MyComments = cellValue?.ToString() ?? string.Empty;
                    break;

                default:
                    // Store in additional columns
                    transaction.AdditionalColumns[columnName] = cellValue ?? string.Empty;
                    break;
            }
        }

        public void SaveExcelFile(List<BankTransaction> transactions, string? outputPath = null)
        {
            if (_worksheet == null || _workbook == null)
                throw new InvalidOperationException("No Excel file is currently loaded.");

            try
            {
                // Find the "My Comments" column
                int commentsColumnIndex = -1;
                for (int i = 0; i < ColumnNames.Count; i++)
                {
                    if (ColumnNames[i].Replace(" ", "").ToLowerInvariant().Contains("mycomments") ||
                        ColumnNames[i].Replace(" ", "").ToLowerInvariant().Contains("comments"))
                    {
                        commentsColumnIndex = i + 1; // Excel is 1-based
                        break;
                    }
                }

                // If no comments column exists, add one
                if (commentsColumnIndex == -1)
                {
                    commentsColumnIndex = ColumnNames.Count + 1;
                    var headerCell = _worksheet.Cell(1, commentsColumnIndex);
                    headerCell.Value = "My Comments";
                    ColumnNames.Add("My Comments");
                }

                // Update the comments for each transaction
                for (int i = 0; i < transactions.Count; i++)
                {
                    var transaction = transactions[i];
                    var cell = _worksheet.Cell(i + 2, commentsColumnIndex); // +2 because Excel is 1-based and we skip header
                    cell.Value = transaction.MyComments ?? string.Empty;
                }

                // Save the file
                if (!string.IsNullOrEmpty(outputPath))
                {
                    // Ensure the output path has the correct extension
                    var extension = Path.GetExtension(outputPath).ToLowerInvariant();
                    if (extension != ".xlsx")
                    {
                        outputPath = Path.ChangeExtension(outputPath, ".xlsx");
                    }

                    _workbook.SaveAs(outputPath);
                }
                else
                {
                    _workbook.Save();
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error saving Excel file: {ex.Message}", ex);
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    _workbook?.Dispose();
                }
                catch
                {
                    // Ignore cleanup errors
                }

                _worksheet = null;
                _workbook = null;
                _disposed = true;
            }
        }
    }
}
