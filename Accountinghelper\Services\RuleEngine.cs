using System;
using System.Collections.Generic;
using System.Linq;
using Accountinghelper.Models;

namespace Accountinghelper.Services
{
    public class RuleEngine
    {
        private readonly List<ClassificationRule> _rules;

        public RuleEngine(List<ClassificationRule> rules)
        {
            _rules = rules ?? new List<ClassificationRule>();
        }

        public RuleMatchResult? FindMatchingRule(BankTransaction transaction)
        {
            // Sort rules by priority (highest first)
            var sortedRules = _rules
                .Where(r => r.IsEnabled)
                .OrderByDescending(r => r.Priority)
                .ThenByDescending(r => r.ConfidenceScore);

            foreach (var rule in sortedRules)
            {
                if (rule.EvaluateConditions(transaction))
                {
                    return new RuleMatchResult
                    {
                        Rule = rule,
                        Transaction = transaction,
                        MatchedConditions = rule.Conditions.Where(c => c.Evaluate(transaction)).ToList()
                    };
                }
            }

            return null;
        }

        public List<RuleMatchResult> FindAllMatchingRules(BankTransaction transaction)
        {
            var matches = new List<RuleMatchResult>();

            var sortedRules = _rules
                .Where(r => r.IsEnabled)
                .OrderByDescending(r => r.Priority)
                .ThenByDescending(r => r.ConfidenceScore);

            foreach (var rule in sortedRules)
            {
                if (rule.EvaluateConditions(transaction))
                {
                    matches.Add(new RuleMatchResult
                    {
                        Rule = rule,
                        Transaction = transaction,
                        MatchedConditions = rule.Conditions.Where(c => c.Evaluate(transaction)).ToList()
                    });
                }
            }

            return matches;
        }

        public bool ApplyRule(ClassificationRule rule, BankTransaction transaction)
        {
            if (!rule.IsEnabled || !rule.EvaluateConditions(transaction))
                return false;

            transaction.MyComments = rule.Comment;
            transaction.IsRuleApplied = true;
            rule.IncrementUsage();

            return true;
        }

        public int ApplyRulesToTransactions(List<BankTransaction> transactions, 
            Action<int, int>? progressCallback = null)
        {
            int appliedCount = 0;

            for (int i = 0; i < transactions.Count; i++)
            {
                var transaction = transactions[i];
                
                // Skip if already has a comment (unless it's empty)
                if (!string.IsNullOrWhiteSpace(transaction.MyComments))
                {
                    progressCallback?.Invoke(i + 1, transactions.Count);
                    continue;
                }

                var matchResult = FindMatchingRule(transaction);
                if (matchResult != null)
                {
                    ApplyRule(matchResult.Rule, transaction);
                    appliedCount++;
                }

                progressCallback?.Invoke(i + 1, transactions.Count);
            }

            return appliedCount;
        }

        public List<RuleTestResult> TestRule(ClassificationRule rule, List<BankTransaction> transactions)
        {
            var results = new List<RuleTestResult>();

            foreach (var transaction in transactions)
            {
                var matches = rule.EvaluateConditions(transaction);
                results.Add(new RuleTestResult
                {
                    Transaction = transaction,
                    Matches = matches,
                    MatchedConditions = matches ? rule.Conditions.Where(c => c.Evaluate(transaction)).ToList() : new List<RuleCondition>()
                });
            }

            return results;
        }

        public RuleStatistics GetRuleStatistics(ClassificationRule rule, List<BankTransaction> transactions)
        {
            var testResults = TestRule(rule, transactions);
            var matchingTransactions = testResults.Where(r => r.Matches).ToList();

            return new RuleStatistics
            {
                Rule = rule,
                TotalTransactions = transactions.Count,
                MatchingTransactions = matchingTransactions.Count,
                MatchPercentage = transactions.Count > 0 ? (double)matchingTransactions.Count / transactions.Count * 100 : 0,
                SampleMatches = matchingTransactions.Take(5).Select(r => r.Transaction).ToList()
            };
        }
    }

    public class RuleMatchResult
    {
        public ClassificationRule Rule { get; set; } = null!;
        public BankTransaction Transaction { get; set; } = null!;
        public List<RuleCondition> MatchedConditions { get; set; } = new();
    }

    public class RuleTestResult
    {
        public BankTransaction Transaction { get; set; } = null!;
        public bool Matches { get; set; }
        public List<RuleCondition> MatchedConditions { get; set; } = new();
    }

    public class RuleStatistics
    {
        public ClassificationRule Rule { get; set; } = null!;
        public int TotalTransactions { get; set; }
        public int MatchingTransactions { get; set; }
        public double MatchPercentage { get; set; }
        public List<BankTransaction> SampleMatches { get; set; } = new();
    }
}
