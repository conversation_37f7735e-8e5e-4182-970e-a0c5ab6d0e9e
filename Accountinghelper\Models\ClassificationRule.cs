using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;

namespace Accountinghelper.Models
{
    public class ClassificationRule
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Comment { get; set; } = string.Empty;
        public List<RuleCondition> Conditions { get; set; } = new();
        public LogicalOperator LogicalOperator { get; set; } = LogicalOperator.AND;
        public bool IsEnabled { get; set; } = true;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime LastModifiedDate { get; set; } = DateTime.Now;
        public int Priority { get; set; } = 0; // Higher number = higher priority
        public double ConfidenceScore { get; set; } = 1.0; // For suggested rules from pattern analysis

        // Statistics
        public int TimesApplied { get; set; } = 0;
        public DateTime? LastAppliedDate { get; set; }

        public bool EvaluateConditions(BankTransaction transaction)
        {
            if (!Conditions.Any())
                return false;

            if (LogicalOperator == LogicalOperator.AND)
            {
                return Conditions.All(condition => condition.Evaluate(transaction));
            }
            else // OR
            {
                return Conditions.Any(condition => condition.Evaluate(transaction));
            }
        }

        public void IncrementUsage()
        {
            TimesApplied++;
            LastAppliedDate = DateTime.Now;
            LastModifiedDate = DateTime.Now;
        }

        public override string ToString()
        {
            return $"{Name}: {Comment}";
        }
    }

    public enum LogicalOperator
    {
        AND,
        OR
    }
}
