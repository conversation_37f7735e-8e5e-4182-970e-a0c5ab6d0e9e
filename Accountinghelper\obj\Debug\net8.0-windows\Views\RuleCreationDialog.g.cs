﻿#pragma checksum "..\..\..\..\Views\RuleCreationDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "775CD96F8E837FD406446044BF1ABA08055952C1"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace Accountinghelper.Views {
    
    
    /// <summary>
    /// RuleCreationDialog
    /// </summary>
    public partial class RuleCreationDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 35 "..\..\..\..\Views\RuleCreationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HeaderText;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\..\Views\RuleCreationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NaturalLanguageTextBox;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\Views\RuleCreationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ParseNaturalLanguageButton;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\Views\RuleCreationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ParseResultText;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\..\Views\RuleCreationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MaheshSalaryTemplateButton;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\Views\RuleCreationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SalaryTemplateButton;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\..\Views\RuleCreationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ATMTemplateButton;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\Views\RuleCreationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TransferTemplateButton;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\..\Views\RuleCreationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button InterestTemplateButton;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\Views\RuleCreationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RuleNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\..\Views\RuleCreationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RuleCommentTextBox;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\Views\RuleCreationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RuleDescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\Views\RuleCreationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RulePriorityTextBox;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\..\Views\RuleCreationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ConditionsPanel;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\..\Views\RuleCreationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddConditionButton;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\Views\RuleCreationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox LogicalOperatorComboBox;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\..\Views\RuleCreationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RulePreviewText;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\..\Views\RuleCreationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TestResultsHeader;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\Views\RuleCreationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TestResultsText;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\..\Views\RuleCreationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestRuleButton;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\Views\RuleCreationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveRuleButton;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\Views\RuleCreationDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/Accountinghelper;component/views/rulecreationdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\RuleCreationDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.HeaderText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.NaturalLanguageTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 53 "..\..\..\..\Views\RuleCreationDialog.xaml"
            this.NaturalLanguageTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.NaturalLanguageTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ParseNaturalLanguageButton = ((System.Windows.Controls.Button)(target));
            
            #line 58 "..\..\..\..\Views\RuleCreationDialog.xaml"
            this.ParseNaturalLanguageButton.Click += new System.Windows.RoutedEventHandler(this.ParseNaturalLanguage_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.ParseResultText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.MaheshSalaryTemplateButton = ((System.Windows.Controls.Button)(target));
            
            #line 80 "..\..\..\..\Views\RuleCreationDialog.xaml"
            this.MaheshSalaryTemplateButton.Click += new System.Windows.RoutedEventHandler(this.MaheshSalaryTemplateButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.SalaryTemplateButton = ((System.Windows.Controls.Button)(target));
            
            #line 81 "..\..\..\..\Views\RuleCreationDialog.xaml"
            this.SalaryTemplateButton.Click += new System.Windows.RoutedEventHandler(this.SalaryTemplateButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ATMTemplateButton = ((System.Windows.Controls.Button)(target));
            
            #line 82 "..\..\..\..\Views\RuleCreationDialog.xaml"
            this.ATMTemplateButton.Click += new System.Windows.RoutedEventHandler(this.ATMTemplateButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.TransferTemplateButton = ((System.Windows.Controls.Button)(target));
            
            #line 83 "..\..\..\..\Views\RuleCreationDialog.xaml"
            this.TransferTemplateButton.Click += new System.Windows.RoutedEventHandler(this.TransferTemplateButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.InterestTemplateButton = ((System.Windows.Controls.Button)(target));
            
            #line 84 "..\..\..\..\Views\RuleCreationDialog.xaml"
            this.InterestTemplateButton.Click += new System.Windows.RoutedEventHandler(this.InterestTemplateButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.RuleNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.RuleCommentTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.RuleDescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.RulePriorityTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.ConditionsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 15:
            this.AddConditionButton = ((System.Windows.Controls.Button)(target));
            
            #line 112 "..\..\..\..\Views\RuleCreationDialog.xaml"
            this.AddConditionButton.Click += new System.Windows.RoutedEventHandler(this.AddCondition_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.LogicalOperatorComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 17:
            this.RulePreviewText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.TestResultsHeader = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.TestResultsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.TestRuleButton = ((System.Windows.Controls.Button)(target));
            
            #line 148 "..\..\..\..\Views\RuleCreationDialog.xaml"
            this.TestRuleButton.Click += new System.Windows.RoutedEventHandler(this.TestRule_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.SaveRuleButton = ((System.Windows.Controls.Button)(target));
            
            #line 149 "..\..\..\..\Views\RuleCreationDialog.xaml"
            this.SaveRuleButton.Click += new System.Windows.RoutedEventHandler(this.SaveRule_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 150 "..\..\..\..\Views\RuleCreationDialog.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.Cancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

