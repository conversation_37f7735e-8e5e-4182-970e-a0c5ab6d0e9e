using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace Accountinghelper.Models
{
    public class BankTransaction : INotifyPropertyChanged
    {
        private string _myComments = string.Empty;
        private bool _isCurrentRow = false;
        private bool _isRuleApplied = false;

        public int RowIndex { get; set; }
        public DateTime? Date { get; set; }
        public DateTime? ValueDate { get; set; }
        public DateTime? TransactionDate { get; set; }
        public DateTime? TransactionPostedDate { get; set; }
        public string TransactionRemarks { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public decimal? Amount { get; set; }
        public decimal? DebitAmount { get; set; }
        public decimal? CreditAmount { get; set; }
        public decimal? Balance { get; set; }
        public string ReferenceNumber { get; set; } = string.Empty;
        public string TransactionType { get; set; } = string.Empty;
        public string Branch { get; set; } = string.Empty;

        // Additional properties that might be in Excel files
        public Dictionary<string, object> AdditionalColumns { get; set; } = new();

        public string MyComments
        {
            get => _myComments;
            set
            {
                if (_myComments != value)
                {
                    _myComments = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsCurrentRow
        {
            get => _isCurrentRow;
            set
            {
                if (_isCurrentRow != value)
                {
                    _isCurrentRow = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsRuleApplied
        {
            get => _isRuleApplied;
            set
            {
                if (_isRuleApplied != value)
                {
                    _isRuleApplied = value;
                    OnPropertyChanged();
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        // Method to get value by column name (with spaces removed)
        public object? GetValueByColumnName(string columnName)
        {
            var normalizedName = columnName.Replace(" ", "").ToLowerInvariant();
            
            return normalizedName switch
            {
                "date" => Date,
                "transactionremarks" => TransactionRemarks,
                "description" => Description,
                "amount" => Amount,
                "debitamount" => DebitAmount,
                "creditamount" => CreditAmount,
                "balance" => Balance,
                "referencenumber" => ReferenceNumber,
                "transactiontype" => TransactionType,
                "branch" => Branch,
                "mycomments" => MyComments,
                _ => AdditionalColumns.TryGetValue(columnName, out var value) ? value : null
            };
        }

        // Method to set value by column name
        public void SetValueByColumnName(string columnName, object? value)
        {
            var normalizedName = columnName.Replace(" ", "").ToLowerInvariant();
            
            switch (normalizedName)
            {
                case "mycomments":
                    MyComments = value?.ToString() ?? string.Empty;
                    break;
                default:
                    if (!AdditionalColumns.ContainsKey(columnName))
                        AdditionalColumns[columnName] = value ?? string.Empty;
                    break;
            }
        }
    }
}
