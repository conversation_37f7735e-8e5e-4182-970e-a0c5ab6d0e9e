﻿#pragma checksum "..\..\..\..\Views\RuleTestDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "1E70E45443BB2DCD25B00D74AD29EBDBC40A4564"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace Accountinghelper.Views {
    
    
    /// <summary>
    /// RuleTestDialog
    /// </summary>
    public partial class RuleTestDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 18 "..\..\..\..\Views\RuleTestDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RuleNameText;
        
        #line default
        #line hidden
        
        
        #line 19 "..\..\..\..\Views\RuleTestDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RuleConditionsText;
        
        #line default
        #line hidden
        
        
        #line 26 "..\..\..\..\Views\RuleTestDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TestSummaryText;
        
        #line default
        #line hidden
        
        
        #line 32 "..\..\..\..\Views\RuleTestDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid SampleTransactionsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\..\Views\RuleTestDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ApplyToAllButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/Accountinghelper;component/views/ruletestdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\RuleTestDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.RuleNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.RuleConditionsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.TestSummaryText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.SampleTransactionsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 5:
            this.ApplyToAllButton = ((System.Windows.Controls.Button)(target));
            
            #line 59 "..\..\..\..\Views\RuleTestDialog.xaml"
            this.ApplyToAllButton.Click += new System.Windows.RoutedEventHandler(this.ApplyToAll_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 64 "..\..\..\..\Views\RuleTestDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Close_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

