using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Accountinghelper.Models;
using Accountinghelper.Services;

namespace Accountinghelper.Views
{
    public partial class RuleCreationDialog : Window
    {
        private readonly RuleManager _ruleManager;
        private ClassificationRule? _editingRule;
        private List<ConditionControl> _conditionControls = new();
        private List<string> _availableColumns = new();
        private List<BankTransaction>? _transactionData;

        public RuleCreationDialog(RuleManager ruleManager, ClassificationRule? editingRule = null, List<BankTransaction>? transactionData = null)
        {
            InitializeComponent();
            _ruleManager = ruleManager;
            _editingRule = editingRule;
            _transactionData = transactionData;

            InitializeAvailableColumns();
            
            if (_editingRule != null)
            {
                HeaderText.Text = "Edit Rule";
                LoadRuleForEditing();
            }
            
            UpdateRulePreview();
        }

        private void InitializeAvailableColumns()
        {
            // Updated to match your actual Excel file columns
            _availableColumns = new List<string>
            {
                "SN", "TranId", "ValueDate", "TransactionDate", "TransactionPostedDate",
                "TransactionRemarks", "WithdrawalAmtINR", "DepositAmtINR", "BalanceINR", "MyComments"
            };
        }

        private void LoadRuleForEditing()
        {
            if (_editingRule == null) return;

            RuleNameTextBox.Text = _editingRule.Name;
            RuleCommentTextBox.Text = _editingRule.Comment;
            RuleDescriptionTextBox.Text = _editingRule.Description;
            RulePriorityTextBox.Text = _editingRule.Priority.ToString();
            LogicalOperatorComboBox.SelectedIndex = _editingRule.LogicalOperator == LogicalOperator.AND ? 0 : 1;

            // Load conditions
            foreach (var condition in _editingRule.Conditions)
            {
                AddConditionControl(condition);
            }
        }

        private void NaturalLanguageTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ParseResultText.Text = "";
        }

        private void ParseNaturalLanguage_Click(object sender, RoutedEventArgs e)
        {
            var input = NaturalLanguageTextBox.Text.Trim();
            if (string.IsNullOrEmpty(input))
            {
                ParseResultText.Text = "Please enter a rule description.";
                ParseResultText.Foreground = System.Windows.Media.Brushes.Red;
                return;
            }

            try
            {
                var parsedRule = ParseNaturalLanguageRule(input);
                if (parsedRule != null)
                {
                    // Apply parsed rule to visual builder
                    RuleNameTextBox.Text = parsedRule.Name;
                    RuleCommentTextBox.Text = parsedRule.Comment;
                    RuleDescriptionTextBox.Text = parsedRule.Description;
                    
                    // Clear existing conditions
                    _conditionControls.Clear();
                    ConditionsPanel.Children.Clear();
                    
                    // Add parsed conditions
                    foreach (var condition in parsedRule.Conditions)
                    {
                        AddConditionControl(condition);
                    }
                    
                    LogicalOperatorComboBox.SelectedIndex = parsedRule.LogicalOperator == LogicalOperator.AND ? 0 : 1;
                    
                    ParseResultText.Text = "Rule parsed successfully! Check the Visual Builder tab.";
                    ParseResultText.Foreground = System.Windows.Media.Brushes.Green;
                    
                    UpdateRulePreview();
                }
                else
                {
                    ParseResultText.Text = "Could not parse the rule. Please try rephrasing or use the Visual Builder.";
                    ParseResultText.Foreground = System.Windows.Media.Brushes.Orange;
                }
            }
            catch (Exception ex)
            {
                ParseResultText.Text = $"Error parsing rule: {ex.Message}";
                ParseResultText.Foreground = System.Windows.Media.Brushes.Red;
            }
        }

        private ClassificationRule? ParseNaturalLanguageRule(string input)
        {
            var rule = new ClassificationRule();

            // Extract classification comment (what the transaction should be classified as)
            // Fixed regex: capture everything after "should be" until end of string
            var shouldBeMatch = Regex.Match(input, @"should be (.+)$", RegexOptions.IgnoreCase);
            if (shouldBeMatch.Success)
            {
                rule.Comment = shouldBeMatch.Groups[1].Value.Trim();
                rule.Name = $"Auto: {rule.Comment}";
            }
            else
            {
                // Try alternative patterns - also fixed to capture complete phrase
                var classifyAsMatch = Regex.Match(input, @"classify as (.+)$", RegexOptions.IgnoreCase);
                if (classifyAsMatch.Success)
                {
                    rule.Comment = classifyAsMatch.Groups[1].Value.Trim();
                    rule.Name = $"Auto: {rule.Comment}";
                }
            }

            if (string.IsNullOrEmpty(rule.Comment))
                return null;

            rule.Description = $"Generated from: {input}";

            // Parse conditions
            var conditions = new List<RuleCondition>();

            // Pattern: "containing X" or "contains X"
            var containsMatches = Regex.Matches(input, @"(?:containing|contains)\s+['""]?([^'""]+?)['""]?(?:\s|$)", RegexOptions.IgnoreCase);
            foreach (Match match in containsMatches)
            {
                var value = match.Groups[1].Value.Trim();
                // Remove spaces for multi-word values as specified in requirements
                var processedValue = value.Replace(" ", "");
                conditions.Add(new RuleCondition
                {
                    ColumnName = "TransactionRemarks",
                    Operator = ComparisonOperator.CONTAINS,
                    Value = processedValue,
                    IsCaseSensitive = false
                });
            }

            // Pattern: "amount greater than X" or "amounts > X"
            var greaterThanMatches = Regex.Matches(input, @"amount[s]?\s+(?:greater than|>)\s+(\d+(?:\.\d+)?)", RegexOptions.IgnoreCase);
            foreach (Match match in greaterThanMatches)
            {
                conditions.Add(new RuleCondition
                {
                    ColumnName = "Amount",
                    Operator = ComparisonOperator.GREATER_THAN,
                    Value = match.Groups[1].Value,
                    IsCaseSensitive = false
                });
            }

            // Pattern: "amount less than X" or "amounts < X"
            var lessThanMatches = Regex.Matches(input, @"amount[s]?\s+(?:less than|<)\s+(\d+(?:\.\d+)?)", RegexOptions.IgnoreCase);
            foreach (Match match in lessThanMatches)
            {
                conditions.Add(new RuleCondition
                {
                    ColumnName = "Amount",
                    Operator = ComparisonOperator.LESS_THAN,
                    Value = match.Groups[1].Value,
                    IsCaseSensitive = false
                });
            }

            // Pattern: "description is X" or "description equals X"
            var descriptionMatches = Regex.Matches(input, @"description\s+(?:is|equals)\s+['""]?([^'""]+?)['""]?(?:\s|$)", RegexOptions.IgnoreCase);
            foreach (Match match in descriptionMatches)
            {
                conditions.Add(new RuleCondition
                {
                    ColumnName = "Description",
                    Operator = ComparisonOperator.EQUALS,
                    Value = match.Groups[1].Value.Trim(),
                    IsCaseSensitive = false
                });
            }

            // Pattern: "reference number is X" or "reference X"
            var referenceMatches = Regex.Matches(input, @"(?:reference\s+(?:number\s+)?(?:is\s+)?|ref\s+)([A-Za-z0-9]+)", RegexOptions.IgnoreCase);
            foreach (Match match in referenceMatches)
            {
                conditions.Add(new RuleCondition
                {
                    ColumnName = "ReferenceNumber",
                    Operator = ComparisonOperator.EQUALS,
                    Value = match.Groups[1].Value.Trim(),
                    IsCaseSensitive = false
                });
            }

            // Pattern: "branch is X" or "from branch X"
            var branchMatches = Regex.Matches(input, @"(?:branch\s+(?:is\s+)?|from\s+branch\s+)['""]?([^'""]+?)['""]?(?:\s|$)", RegexOptions.IgnoreCase);
            foreach (Match match in branchMatches)
            {
                conditions.Add(new RuleCondition
                {
                    ColumnName = "Branch",
                    Operator = ComparisonOperator.EQUALS,
                    Value = match.Groups[1].Value.Trim(),
                    IsCaseSensitive = false
                });
            }

            // Pattern: "amount equals X" or "amount is X"
            var amountEqualsMatches = Regex.Matches(input, @"amount\s+(?:equals|is)\s+(\d+(?:\.\d+)?)", RegexOptions.IgnoreCase);
            foreach (Match match in amountEqualsMatches)
            {
                conditions.Add(new RuleCondition
                {
                    ColumnName = "Amount",
                    Operator = ComparisonOperator.EQUALS,
                    Value = match.Groups[1].Value,
                    IsCaseSensitive = false
                });
            }

            // Pattern: "debit amount" or "credit amount"
            var debitMatches = Regex.Matches(input, @"debit\s+amount\s+(?:greater than|>)\s+(\d+(?:\.\d+)?)", RegexOptions.IgnoreCase);
            foreach (Match match in debitMatches)
            {
                conditions.Add(new RuleCondition
                {
                    ColumnName = "DebitAmount",
                    Operator = ComparisonOperator.GREATER_THAN,
                    Value = match.Groups[1].Value,
                    IsCaseSensitive = false
                });
            }

            var creditMatches = Regex.Matches(input, @"credit\s+amount\s+(?:greater than|>)\s+(\d+(?:\.\d+)?)", RegexOptions.IgnoreCase);
            foreach (Match match in creditMatches)
            {
                conditions.Add(new RuleCondition
                {
                    ColumnName = "CreditAmount",
                    Operator = ComparisonOperator.GREATER_THAN,
                    Value = match.Groups[1].Value,
                    IsCaseSensitive = false
                });
            }

            // Pattern: Date ranges
            var dateRangeMatches = Regex.Matches(input, @"(?:from|after|since)\s+(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})", RegexOptions.IgnoreCase);
            foreach (Match match in dateRangeMatches)
            {
                if (DateTime.TryParse(match.Groups[1].Value, out var date))
                {
                    conditions.Add(new RuleCondition
                    {
                        ColumnName = "Date",
                        Operator = ComparisonOperator.GREATER_THAN_OR_EQUAL,
                        Value = date.ToString("yyyy-MM-dd"),
                        IsCaseSensitive = false
                    });
                }
            }

            var beforeDateMatches = Regex.Matches(input, @"(?:before|until)\s+(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})", RegexOptions.IgnoreCase);
            foreach (Match match in beforeDateMatches)
            {
                if (DateTime.TryParse(match.Groups[1].Value, out var date))
                {
                    conditions.Add(new RuleCondition
                    {
                        ColumnName = "Date",
                        Operator = ComparisonOperator.LESS_THAN_OR_EQUAL,
                        Value = date.ToString("yyyy-MM-dd"),
                        IsCaseSensitive = false
                    });
                }
            }

            // Pattern: "last month", "this month", etc.
            var lastMonthMatch = Regex.Match(input, @"last\s+month", RegexOptions.IgnoreCase);
            if (lastMonthMatch.Success)
            {
                var lastMonth = DateTime.Now.AddMonths(-1);
                var startOfLastMonth = new DateTime(lastMonth.Year, lastMonth.Month, 1);
                var endOfLastMonth = startOfLastMonth.AddMonths(1).AddDays(-1);

                conditions.Add(new RuleCondition
                {
                    ColumnName = "Date",
                    Operator = ComparisonOperator.GREATER_THAN_OR_EQUAL,
                    Value = startOfLastMonth.ToString("yyyy-MM-dd"),
                    IsCaseSensitive = false
                });

                conditions.Add(new RuleCondition
                {
                    ColumnName = "Date",
                    Operator = ComparisonOperator.LESS_THAN_OR_EQUAL,
                    Value = endOfLastMonth.ToString("yyyy-MM-dd"),
                    IsCaseSensitive = false
                });
            }

            var thisMonthMatch = Regex.Match(input, @"this\s+month", RegexOptions.IgnoreCase);
            if (thisMonthMatch.Success)
            {
                var now = DateTime.Now;
                var startOfThisMonth = new DateTime(now.Year, now.Month, 1);

                conditions.Add(new RuleCondition
                {
                    ColumnName = "Date",
                    Operator = ComparisonOperator.GREATER_THAN_OR_EQUAL,
                    Value = startOfThisMonth.ToString("yyyy-MM-dd"),
                    IsCaseSensitive = false
                });
            }

            rule.Conditions = conditions;

            // Detect logical operator from input
            if (input.Contains(" or ", StringComparison.OrdinalIgnoreCase) ||
                input.Contains(" OR ", StringComparison.Ordinal))
            {
                rule.LogicalOperator = LogicalOperator.OR;
            }
            else
            {
                rule.LogicalOperator = LogicalOperator.AND; // Default to AND
            }

            return conditions.Any() ? rule : null;
        }

        private void AddCondition_Click(object sender, RoutedEventArgs e)
        {
            AddConditionControl();
            UpdateRulePreview();
        }

        private void AddConditionControl(RuleCondition? condition = null)
        {
            var conditionControl = new ConditionControl(_availableColumns, condition, _transactionData);
            conditionControl.ConditionChanged += (s, e) => UpdateRulePreview();
            conditionControl.RemoveRequested += (s, e) => RemoveConditionControl(conditionControl);

            _conditionControls.Add(conditionControl);
            ConditionsPanel.Children.Add(conditionControl);
        }

        private void RemoveConditionControl(ConditionControl control)
        {
            _conditionControls.Remove(control);
            ConditionsPanel.Children.Remove(control);
            UpdateRulePreview();
        }

        private void UpdateRulePreview()
        {
            if (_conditionControls.Count == 0)
            {
                RulePreviewText.Text = "No conditions defined";
                return;
            }

            var conditions = _conditionControls
                .Where(c => c.IsValid())
                .Select(c => c.GetCondition().ToString())
                .ToList();

            if (!conditions.Any())
            {
                RulePreviewText.Text = "No valid conditions defined";
                return;
            }

            var logicalOp = LogicalOperatorComboBox.SelectedIndex == 0 ? "AND" : "OR";
            var preview = string.Join($" {logicalOp} ", conditions);

            if (!string.IsNullOrWhiteSpace(RuleCommentTextBox.Text))
            {
                preview += $" → \"{RuleCommentTextBox.Text}\"";
            }

            RulePreviewText.Text = preview;
        }

        private void TestRule_Click(object sender, RoutedEventArgs e)
        {
            var rule = BuildRuleFromUI();
            if (rule == null)
            {
                MessageBox.Show("Please define at least one valid condition.", "Invalid Rule",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // Show test results
            TestResultsHeader.Visibility = Visibility.Visible;
            TestResultsText.Visibility = Visibility.Visible;
            TestResultsText.Text = "Rule test functionality will show matching transactions here.";
        }

        private void SaveRule_Click(object sender, RoutedEventArgs e)
        {
            var rule = BuildRuleFromUI();
            if (rule == null)
            {
                MessageBox.Show("Please define at least one valid condition.", "Invalid Rule",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(rule.Name))
            {
                MessageBox.Show("Please enter a rule name.", "Missing Name",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(rule.Comment))
            {
                MessageBox.Show("Please enter a classification comment.", "Missing Comment",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                if (_editingRule != null)
                {
                    rule.Id = _editingRule.Id;
                    rule.CreatedDate = _editingRule.CreatedDate;
                    rule.TimesApplied = _editingRule.TimesApplied;
                    rule.LastAppliedDate = _editingRule.LastAppliedDate;
                    _ruleManager.UpdateRule(rule);
                }
                else
                {
                    _ruleManager.AddRule(rule);
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving rule: {ex.Message}", "Save Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        // Quick Template Event Handlers
        private void MaheshSalaryTemplateButton_Click(object sender, RoutedEventArgs e)
        {
            ApplyTemplate("Mahesh Salary Rule", "Mahesh Salary", "TransactionRemarks", "Contains", "MaheshSalary");
        }

        private void SalaryTemplateButton_Click(object sender, RoutedEventArgs e)
        {
            ApplyTemplate("General Salary Transaction", "Salary", "TransactionRemarks", "Contains", "Salary");
        }

        private void ATMTemplateButton_Click(object sender, RoutedEventArgs e)
        {
            ApplyTemplate("ATM Withdrawal", "ATM Transaction", "TransactionRemarks", "Contains", "ATM");
        }

        private void TransferTemplateButton_Click(object sender, RoutedEventArgs e)
        {
            ApplyTemplate("Bank Transfer", "Transfer", "TransactionRemarks", "Contains", "Transfer");
        }

        private void InterestTemplateButton_Click(object sender, RoutedEventArgs e)
        {
            ApplyTemplate("Interest Credit", "Interest", "TransactionRemarks", "Contains", "Interest");
        }

        private void ApplyTemplate(string ruleName, string comment, string column, string operatorType, string value)
        {
            // Clear existing conditions
            ConditionsPanel.Children.Clear();

            // Set basic info
            RuleNameTextBox.Text = ruleName;
            RuleCommentTextBox.Text = comment;

            // Create the condition
            var condition = new RuleCondition
            {
                ColumnName = column,
                Operator = ComparisonOperator.CONTAINS, // Default to CONTAINS for templates
                Value = value
            };

            // Add the condition control
            var conditionControl = new ConditionControl(_availableColumns, condition, _transactionData);
            conditionControl.ConditionChanged += (s, e) => UpdateRulePreview();
            conditionControl.RemoveRequested += (s, e) =>
            {
                ConditionsPanel.Children.Remove(conditionControl);
                UpdateRulePreview();
            };
            ConditionsPanel.Children.Add(conditionControl);

            // Update preview
            UpdateRulePreview();

            // Show success message in the Natural Language tab
            ParseResultText.Text = $"✓ Applied template: {ruleName}";
            ParseResultText.Foreground = Brushes.Green;
        }

        private ClassificationRule? BuildRuleFromUI()
        {
            var validConditions = _conditionControls
                .Where(c => c.IsValid())
                .Select(c => c.GetCondition())
                .ToList();

            if (!validConditions.Any())
                return null;

            var rule = new ClassificationRule
            {
                Name = RuleNameTextBox.Text.Trim(),
                Comment = RuleCommentTextBox.Text.Trim(),
                Description = RuleDescriptionTextBox.Text.Trim(),
                Conditions = validConditions,
                LogicalOperator = LogicalOperatorComboBox.SelectedIndex == 0 ? LogicalOperator.AND : LogicalOperator.OR
            };

            if (int.TryParse(RulePriorityTextBox.Text, out var priority))
                rule.Priority = priority;

            return rule;
        }
    }
}
