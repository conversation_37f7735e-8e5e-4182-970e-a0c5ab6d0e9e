using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using Accountinghelper.Models;

namespace Accountinghelper.Services
{
    public class RuleManager
    {
        private readonly string _rulesFilePath;
        private List<ClassificationRule> _rules;

        public RuleManager(string? rulesFilePath = null)
        {
            _rulesFilePath = rulesFilePath ?? Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "AccountingHelper",
                "rules.json");
            
            _rules = new List<ClassificationRule>();
            LoadRules();
        }

        public List<ClassificationRule> GetAllRules()
        {
            return _rules.ToList();
        }

        public ClassificationRule? GetRuleById(Guid id)
        {
            return _rules.FirstOrDefault(r => r.Id == id);
        }

        public void AddRule(ClassificationRule rule)
        {
            if (rule == null)
                throw new ArgumentNullException(nameof(rule));

            // Ensure unique ID
            if (_rules.Any(r => r.Id == rule.Id))
                rule.Id = Guid.NewGuid();

            rule.CreatedDate = DateTime.Now;
            rule.LastModifiedDate = DateTime.Now;
            
            _rules.Add(rule);
            SaveRules();
        }

        public void UpdateRule(ClassificationRule rule)
        {
            if (rule == null)
                throw new ArgumentNullException(nameof(rule));

            var existingRule = _rules.FirstOrDefault(r => r.Id == rule.Id);
            if (existingRule == null)
                throw new InvalidOperationException($"Rule with ID {rule.Id} not found.");

            var index = _rules.IndexOf(existingRule);
            rule.LastModifiedDate = DateTime.Now;
            _rules[index] = rule;
            SaveRules();
        }

        public void DeleteRule(Guid id)
        {
            var rule = _rules.FirstOrDefault(r => r.Id == id);
            if (rule != null)
            {
                _rules.Remove(rule);
                SaveRules();
            }
        }

        public void DeleteRule(ClassificationRule rule)
        {
            if (rule != null && _rules.Contains(rule))
            {
                _rules.Remove(rule);
                SaveRules();
            }
        }

        public void EnableRule(Guid id, bool enabled = true)
        {
            var rule = _rules.FirstOrDefault(r => r.Id == id);
            if (rule != null)
            {
                rule.IsEnabled = enabled;
                rule.LastModifiedDate = DateTime.Now;
                SaveRules();
            }
        }

        public void SetRulePriority(Guid id, int priority)
        {
            var rule = _rules.FirstOrDefault(r => r.Id == id);
            if (rule != null)
            {
                rule.Priority = priority;
                rule.LastModifiedDate = DateTime.Now;
                SaveRules();
            }
        }

        public List<ClassificationRule> GetRulesByPriority()
        {
            return _rules
                .OrderByDescending(r => r.Priority)
                .ThenByDescending(r => r.ConfidenceScore)
                .ThenBy(r => r.Name)
                .ToList();
        }

        public List<ClassificationRule> SearchRules(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return GetAllRules();

            var term = searchTerm.ToLowerInvariant();
            return _rules.Where(r =>
                r.Name.ToLowerInvariant().Contains(term) ||
                r.Description.ToLowerInvariant().Contains(term) ||
                r.Comment.ToLowerInvariant().Contains(term) ||
                r.Conditions.Any(c => c.ColumnName.ToLowerInvariant().Contains(term) ||
                                     c.Value.ToLowerInvariant().Contains(term))
            ).ToList();
        }

        public void ImportRules(string filePath)
        {
            if (!File.Exists(filePath))
                throw new FileNotFoundException($"Rules file not found: {filePath}");

            try
            {
                var json = File.ReadAllText(filePath);
                var importedRules = JsonSerializer.Deserialize<List<ClassificationRule>>(json);
                
                if (importedRules != null)
                {
                    foreach (var rule in importedRules)
                    {
                        // Generate new IDs to avoid conflicts
                        rule.Id = Guid.NewGuid();
                        rule.CreatedDate = DateTime.Now;
                        rule.LastModifiedDate = DateTime.Now;
                        _rules.Add(rule);
                    }
                    SaveRules();
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error importing rules: {ex.Message}", ex);
            }
        }

        public void ExportRules(string filePath, List<Guid>? ruleIds = null)
        {
            try
            {
                var rulesToExport = ruleIds != null
                    ? _rules.Where(r => ruleIds.Contains(r.Id)).ToList()
                    : _rules.ToList();

                var json = JsonSerializer.Serialize(rulesToExport, new JsonSerializerOptions
                {
                    WriteIndented = true
                });

                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    Directory.CreateDirectory(directory);

                File.WriteAllText(filePath, json);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error exporting rules: {ex.Message}", ex);
            }
        }

        public void LoadRules()
        {
            try
            {
                if (File.Exists(_rulesFilePath))
                {
                    var json = File.ReadAllText(_rulesFilePath);
                    var loadedRules = JsonSerializer.Deserialize<List<ClassificationRule>>(json);
                    _rules = loadedRules ?? new List<ClassificationRule>();
                }
                else
                {
                    _rules = new List<ClassificationRule>();
                }
            }
            catch (Exception ex)
            {
                // If loading fails, start with empty rules list
                _rules = new List<ClassificationRule>();
                // Optionally log the error
                System.Diagnostics.Debug.WriteLine($"Error loading rules: {ex.Message}");
            }
        }

        public void SaveRules()
        {
            try
            {
                var directory = Path.GetDirectoryName(_rulesFilePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    Directory.CreateDirectory(directory);

                var json = JsonSerializer.Serialize(_rules, new JsonSerializerOptions
                {
                    WriteIndented = true
                });

                File.WriteAllText(_rulesFilePath, json);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error saving rules: {ex.Message}", ex);
            }
        }

        public void ClearAllRules()
        {
            _rules.Clear();
            SaveRules();
        }

        public RuleStatistics GetRuleUsageStatistics()
        {
            var totalRules = _rules.Count;
            var enabledRules = _rules.Count(r => r.IsEnabled);
            var usedRules = _rules.Count(r => r.TimesApplied > 0);

            return new RuleStatistics
            {
                TotalTransactions = totalRules,
                MatchingTransactions = enabledRules,
                // Reusing the class for different statistics
            };
        }
    }
}
