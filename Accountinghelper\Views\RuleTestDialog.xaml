<Window x:Class="Accountinghelper.Views.RuleTestDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Test Rule" Height="500" Width="700"
        WindowStartupLocation="CenterOwner">
    
    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Rule Info -->
        <GroupBox Grid.Row="0" Header="Rule Information" Margin="0,0,0,10">
            <StackPanel Margin="10">
                <TextBlock Name="RuleNameText" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBlock Name="RuleConditionsText" TextWrapping="Wrap"/>
            </StackPanel>
        </GroupBox>

        <!-- Test Results Summary -->
        <GroupBox Grid.Row="1" Header="Test Results" Margin="0,0,0,10">
            <StackPanel Margin="10">
                <TextBlock Name="TestSummaryText" FontWeight="Bold"/>
            </StackPanel>
        </GroupBox>

        <!-- Sample Matches -->
        <GroupBox Grid.Row="2" Header="Sample Matching Transactions">
            <DataGrid Name="SampleTransactionsDataGrid"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      IsReadOnly="True"
                      Margin="10">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="Date" Binding="{Binding Date, StringFormat=dd/MM/yyyy}" Width="80"/>
                    <DataGridTextColumn Header="Transaction Remarks" Binding="{Binding TransactionRemarks}" Width="200"/>
                    <DataGridTextColumn Header="Description" Binding="{Binding Description}" Width="150"/>
                    <DataGridTextColumn Header="Amount" Binding="{Binding Amount, StringFormat=C}" Width="100"/>
                    <DataGridTextColumn Header="Current Comment" Binding="{Binding MyComments}" Width="150"/>
                </DataGrid.Columns>
            </DataGrid>
        </GroupBox>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="3"
                    Orientation="Horizontal"
                    HorizontalAlignment="Right"
                    Margin="0,10,0,0">
            <Button Name="ApplyToAllButton"
                    Content="Apply to All Matches"
                    Width="150"
                    Height="30"
                    Margin="0,0,10,0"
                    Click="ApplyToAll_Click"
                    IsEnabled="False"/>
            <Button Content="Close"
                    Width="100"
                    Height="30"
                    Click="Close_Click"/>
        </StackPanel>
    </Grid>
</Window>
