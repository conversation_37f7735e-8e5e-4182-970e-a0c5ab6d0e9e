using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Reflection;
using Accountinghelper.Models;

namespace Accountinghelper.Views
{
    public partial class ConditionControl : UserControl
    {
        public event EventHandler? ConditionChanged;
        public event EventHandler? RemoveRequested;

        private List<string> _availableColumns;
        private List<BankTransaction>? _transactionData;
        private Dictionary<string, string> _columnCorrections;
        private bool _isValidatingColumn = false;

        public ConditionControl(List<string> availableColumns, RuleCondition? condition = null, List<BankTransaction>? transactionData = null)
        {
            InitializeComponent();
            _availableColumns = availableColumns;
            _transactionData = transactionData;
            _columnCorrections = InitializeColumnCorrections();
            InitializeControls();

            if (condition != null)
                LoadCondition(condition);
        }

        private Dictionary<string, string> InitializeColumnCorrections()
        {
            return new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
            {
                // Common misspellings and variations for your Excel columns
                {"sn", "SN"},
                {"serialnumber", "SN"},
                {"serial", "SN"},
                {"tranid", "TranId"},
                {"transactionid", "TranId"},
                {"txnid", "TranId"},
                {"id", "TranId"},
                {"valuedate", "ValueDate"},
                {"value", "ValueDate"},
                {"date", "ValueDate"},
                {"transactiondate", "TransactionDate"},
                {"txndate", "TransactionDate"},
                {"transactionposteddate", "TransactionPostedDate"},
                {"posteddate", "TransactionPostedDate"},
                {"posted", "TransactionPostedDate"},
                {"transactionremarks", "TransactionRemarks"},
                {"transactionremark", "TransactionRemarks"},
                {"remarks", "TransactionRemarks"},
                {"remark", "TransactionRemarks"},
                {"txnremarks", "TransactionRemarks"},
                {"withdrawalamtinr", "WithdrawalAmtINR"},
                {"withdrawal", "WithdrawalAmtINR"},
                {"withdrawalamt", "WithdrawalAmtINR"},
                {"debit", "WithdrawalAmtINR"},
                {"debitamount", "WithdrawalAmtINR"},
                {"depositamtinr", "DepositAmtINR"},
                {"deposit", "DepositAmtINR"},
                {"depositamt", "DepositAmtINR"},
                {"credit", "DepositAmtINR"},
                {"creditamount", "DepositAmtINR"},
                {"balanceinr", "BalanceINR"},
                {"balance", "BalanceINR"},
                {"bal", "BalanceINR"},
                {"mycomments", "MyComments"},
                {"comments", "MyComments"},
                {"comment", "MyComments"}
            };
        }

        private void InitializeControls()
        {
            // Populate column dropdown
            ColumnComboBox.ItemsSource = _availableColumns;
            if (_availableColumns.Any())
                ColumnComboBox.SelectedIndex = 0;

            // Populate operator dropdown
            var operators = new List<OperatorItem>
            {
                new OperatorItem { Display = "Contains", Value = ComparisonOperator.CONTAINS },
                new OperatorItem { Display = "Equals", Value = ComparisonOperator.EQUALS },
                new OperatorItem { Display = "Starts With", Value = ComparisonOperator.STARTS_WITH },
                new OperatorItem { Display = "Ends With", Value = ComparisonOperator.ENDS_WITH },
                new OperatorItem { Display = "Greater Than", Value = ComparisonOperator.GREATER_THAN },
                new OperatorItem { Display = "Less Than", Value = ComparisonOperator.LESS_THAN },
                new OperatorItem { Display = "Greater Than or Equal", Value = ComparisonOperator.GREATER_THAN_OR_EQUAL },
                new OperatorItem { Display = "Less Than or Equal", Value = ComparisonOperator.LESS_THAN_OR_EQUAL },
                new OperatorItem { Display = "Not Equals", Value = ComparisonOperator.NOT_EQUALS },
                new OperatorItem { Display = "Not Contains", Value = ComparisonOperator.NOT_CONTAINS }
            };

            OperatorComboBox.ItemsSource = operators;
            OperatorComboBox.DisplayMemberPath = "Display";
            OperatorComboBox.SelectedValuePath = "Value";
            OperatorComboBox.SelectedIndex = 0; // Default to "Contains"
        }

        private void LoadCondition(RuleCondition condition)
        {
            // Set column
            var columnIndex = _availableColumns.IndexOf(condition.ColumnName);
            if (columnIndex >= 0)
                ColumnComboBox.SelectedIndex = columnIndex;

            // Set operator
            OperatorComboBox.SelectedValue = condition.Operator;

            // Set value
            ValueComboBox.Text = condition.Value;
        }

        public bool IsValid()
        {
            return ColumnComboBox.SelectedItem != null &&
                   OperatorComboBox.SelectedItem != null &&
                   !string.IsNullOrWhiteSpace(ValueComboBox.Text);
        }

        public RuleCondition GetCondition()
        {
            return new RuleCondition
            {
                ColumnName = ColumnComboBox.SelectedItem?.ToString() ?? "",
                Operator = (ComparisonOperator)(OperatorComboBox.SelectedValue ?? ComparisonOperator.CONTAINS),
                Value = ValueComboBox.Text.Trim(),
                IsCaseSensitive = false
            };
        }

        private void ColumnComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (!_isValidatingColumn)
            {
                ValidateColumnName();
                ValidateOperatorForColumn();
                UpdateValueSuggestions();
                UpdatePreview();
                ConditionChanged?.Invoke(this, EventArgs.Empty);
            }
        }

        private void ColumnComboBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (!_isValidatingColumn)
            {
                ValidateColumnName();
                ValidateOperatorForColumn();
                UpdateValueSuggestions();
                UpdatePreview();
                ConditionChanged?.Invoke(this, EventArgs.Empty);
            }
        }

        private void ValidateColumnName()
        {
            _isValidatingColumn = true;

            var inputText = ColumnComboBox.Text?.Trim() ?? "";

            if (string.IsNullOrEmpty(inputText))
            {
                SetValidationState(ValidationState.Invalid, "Column name is required");
                _isValidatingColumn = false;
                return;
            }

            // Check if it's an exact match
            if (_availableColumns.Contains(inputText, StringComparer.OrdinalIgnoreCase))
            {
                var exactMatch = _availableColumns.First(c => string.Equals(c, inputText, StringComparison.OrdinalIgnoreCase));
                if (ColumnComboBox.Text != exactMatch)
                {
                    ColumnComboBox.Text = exactMatch;
                }
                SetValidationState(ValidationState.Valid, $"✓ Valid column: {exactMatch}");
                _isValidatingColumn = false;
                return;
            }

            // Check for auto-corrections
            if (_columnCorrections.TryGetValue(inputText, out var correctedName))
            {
                ColumnComboBox.Text = correctedName;
                SetValidationState(ValidationState.Corrected, $"Auto-corrected '{inputText}' → '{correctedName}'");
                _isValidatingColumn = false;
                return;
            }

            // Check for partial matches (fuzzy matching)
            var partialMatches = _availableColumns
                .Where(col => col.Contains(inputText, StringComparison.OrdinalIgnoreCase) ||
                             inputText.Contains(col, StringComparison.OrdinalIgnoreCase))
                .ToList();

            if (partialMatches.Count == 1)
            {
                ColumnComboBox.Text = partialMatches[0];
                SetValidationState(ValidationState.Corrected, $"Auto-corrected '{inputText}' → '{partialMatches[0]}'");
                _isValidatingColumn = false;
                return;
            }

            if (partialMatches.Count > 1)
            {
                SetValidationState(ValidationState.Invalid,
                    $"Multiple matches found: {string.Join(", ", partialMatches)}. Please be more specific.");
                _isValidatingColumn = false;
                return;
            }

            // No matches found
            SetValidationState(ValidationState.Invalid,
                $"Invalid column '{inputText}'. Valid columns: {string.Join(", ", _availableColumns)}");

            _isValidatingColumn = false;
        }

        private void SetValidationState(ValidationState state, string message)
        {
            ValidationMessageText.Text = message;
            ValidationMessageText.Visibility = Visibility.Visible;

            switch (state)
            {
                case ValidationState.Valid:
                    ColumnComboBox.Style = (Style)FindResource("ValidColumnStyle");
                    ValidationMessageText.Foreground = Brushes.Green;
                    break;
                case ValidationState.Corrected:
                    ColumnComboBox.Style = (Style)FindResource("CorrectedColumnStyle");
                    ValidationMessageText.Foreground = Brushes.Orange;
                    break;
                case ValidationState.Invalid:
                    ColumnComboBox.Style = (Style)FindResource("InvalidColumnStyle");
                    ValidationMessageText.Foreground = Brushes.Red;
                    break;
            }
        }

        private void UpdatePreview()
        {
            if (IsValid())
            {
                var condition = GetCondition();
                PreviewText.Text = $"Preview: {condition}";
                PreviewText.Visibility = Visibility.Visible;
            }
            else
            {
                PreviewText.Visibility = Visibility.Collapsed;
            }
        }

        private enum ValidationState
        {
            Valid,
            Corrected,
            Invalid
        }

        private void UpdateValueSuggestions()
        {
            if (_transactionData == null || ColumnComboBox.SelectedItem == null)
                return;

            var selectedColumn = ColumnComboBox.SelectedItem.ToString();
            if (string.IsNullOrEmpty(selectedColumn))
                return;

            try
            {
                var suggestions = GetUniqueValuesForColumn(selectedColumn);

                // Update the ValueComboBox with suggestions
                ValueComboBox.ItemsSource = suggestions;

                // Enable editing and auto-completion
                ValueComboBox.IsEditable = true;
                ValueComboBox.IsTextSearchEnabled = true;
                ValueComboBox.StaysOpenOnEdit = true;
            }
            catch (Exception ex)
            {
                // If there's an error getting suggestions, just clear the suggestions
                ValueComboBox.ItemsSource = null;
                System.Diagnostics.Debug.WriteLine($"Error updating value suggestions: {ex.Message}");
            }
        }

        private List<string> GetUniqueValuesForColumn(string columnName)
        {
            if (_transactionData == null || !_transactionData.Any())
                return new List<string>();

            var values = new HashSet<string>();

            foreach (var transaction in _transactionData)
            {
                var value = GetPropertyValue(transaction, columnName);
                if (!string.IsNullOrWhiteSpace(value))
                {
                    values.Add(value);
                }
            }

            return values.OrderBy(v => v).Take(50).ToList(); // Limit to top 50 suggestions
        }

        private string GetPropertyValue(BankTransaction transaction, string propertyName)
        {
            var property = typeof(BankTransaction).GetProperty(propertyName);
            if (property == null)
                return string.Empty;

            var value = property.GetValue(transaction);
            return value?.ToString() ?? string.Empty;
        }

        private void OperatorComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ValidateOperatorForColumn();
            UpdatePreview();
            ConditionChanged?.Invoke(this, EventArgs.Empty);
        }

        private void ValidateOperatorForColumn()
        {
            if (OperatorComboBox.SelectedValue is ComparisonOperator selectedOperator &&
                ColumnComboBox.SelectedItem is string selectedColumn)
            {
                // Check if numerical operator is being used with non-numerical column
                if (RuleCondition.IsNumericalOperator(selectedOperator) &&
                    !RuleCondition.IsNumericalColumn(selectedColumn))
                {
                    // Show warning and suggest appropriate operators
                    ValidationMessageText.Text = $"⚠️ Numerical operators (>, <, >=, <=) can only be used with amount columns like DepositAmtINR, WithdrawalAmtINR, BalanceINR";
                    ValidationMessageText.Foreground = System.Windows.Media.Brushes.Orange;
                    ValidationMessageText.Visibility = Visibility.Visible;
                }
                else if (!RuleCondition.IsNumericalOperator(selectedOperator) &&
                         RuleCondition.IsNumericalColumn(selectedColumn))
                {
                    // Suggest numerical operators for numerical columns
                    ValidationMessageText.Text = $"💡 For amount columns, consider using numerical operators: >, <, >=, <=";
                    ValidationMessageText.Foreground = System.Windows.Media.Brushes.Blue;
                    ValidationMessageText.Visibility = Visibility.Visible;
                }
                else
                {
                    ValidationMessageText.Visibility = Visibility.Collapsed;
                }
            }
        }

        private void ValueComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdatePreview();
            ConditionChanged?.Invoke(this, EventArgs.Empty);
        }

        private void ValueComboBox_LostFocus(object sender, RoutedEventArgs e)
        {
            UpdatePreview();
            ConditionChanged?.Invoke(this, EventArgs.Empty);
        }

        private void RemoveButton_Click(object sender, RoutedEventArgs e)
        {
            RemoveRequested?.Invoke(this, EventArgs.Empty);
        }

        private class OperatorItem
        {
            public string Display { get; set; } = "";
            public ComparisonOperator Value { get; set; }
        }
    }
}
