﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C97BF9343060CC5BAD6B14B6C53F8A75665E59F9"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using Accountinghelper;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace Accountinghelper {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 76 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenButton;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CreateRuleButton;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ManageRulesButton;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartProcessingButton;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ApplyAllRulesButton;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid BankStatementDataGrid;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentRowInfo;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentTransactionDetails;
        
        #line default
        #line hidden
        
        
        #line 176 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ManualEntryButton;
        
        #line default
        #line hidden
        
        
        #line 183 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ManualCommentTextBox;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ApplyManualCommentButton;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox SuggestedRulesListBox;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ApplySuggestedRuleButton;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RuleNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RuleCommentTextBox;
        
        #line default
        #line hidden
        
        
        #line 222 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ConditionsPanel;
        
        #line default
        #line hidden
        
        
        #line 226 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddConditionButton;
        
        #line default
        #line hidden
        
        
        #line 232 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox LogicalOperatorComboBox;
        
        #line default
        #line hidden
        
        
        #line 240 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestRuleButton;
        
        #line default
        #line hidden
        
        
        #line 245 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveRuleButton;
        
        #line default
        #line hidden
        
        
        #line 259 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PreviousRowButton;
        
        #line default
        #line hidden
        
        
        #line 265 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NextRowButton;
        
        #line default
        #line hidden
        
        
        #line 274 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SkipRowButton;
        
        #line default
        #line hidden
        
        
        #line 280 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button UndoButton;
        
        #line default
        #line hidden
        
        
        #line 295 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 299 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProgressText;
        
        #line default
        #line hidden
        
        
        #line 300 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar ProgressBar;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/Accountinghelper;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 45 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.OpenBankStatement_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 46 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveFile_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 47 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveAsFile_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 49 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ImportRules_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 50 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportRules_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 52 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.Exit_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 55 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ManageRules_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 56 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.CreateRule_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 57 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.AnalyzeReferences_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 59 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyAllRules_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 60 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearClassifications_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 63 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.StartProcessing_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 64 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.NextRow_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 65 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.PreviousRow_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 66 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.SkipRow_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 69 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowShortcuts_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 70 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.About_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.OpenButton = ((System.Windows.Controls.Button)(target));
            
            #line 76 "..\..\..\MainWindow.xaml"
            this.OpenButton.Click += new System.Windows.RoutedEventHandler(this.OpenBankStatement_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 82 "..\..\..\MainWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveFile_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.CreateRuleButton = ((System.Windows.Controls.Button)(target));
            
            #line 89 "..\..\..\MainWindow.xaml"
            this.CreateRuleButton.Click += new System.Windows.RoutedEventHandler(this.CreateRule_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.ManageRulesButton = ((System.Windows.Controls.Button)(target));
            
            #line 95 "..\..\..\MainWindow.xaml"
            this.ManageRulesButton.Click += new System.Windows.RoutedEventHandler(this.ManageRules_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.StartProcessingButton = ((System.Windows.Controls.Button)(target));
            
            #line 102 "..\..\..\MainWindow.xaml"
            this.StartProcessingButton.Click += new System.Windows.RoutedEventHandler(this.StartProcessing_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.ApplyAllRulesButton = ((System.Windows.Controls.Button)(target));
            
            #line 108 "..\..\..\MainWindow.xaml"
            this.ApplyAllRulesButton.Click += new System.Windows.RoutedEventHandler(this.ApplyAllRules_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.BankStatementDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 132 "..\..\..\MainWindow.xaml"
            this.BankStatementDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.BankStatementDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 25:
            this.CurrentRowInfo = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.CurrentTransactionDetails = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 27:
            this.ManualEntryButton = ((System.Windows.Controls.Button)(target));
            
            #line 178 "..\..\..\MainWindow.xaml"
            this.ManualEntryButton.Click += new System.Windows.RoutedEventHandler(this.ManualEntry_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            this.ManualCommentTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 29:
            this.ApplyManualCommentButton = ((System.Windows.Controls.Button)(target));
            
            #line 190 "..\..\..\MainWindow.xaml"
            this.ApplyManualCommentButton.Click += new System.Windows.RoutedEventHandler(this.ApplyManualComment_Click);
            
            #line default
            #line hidden
            return;
            case 30:
            this.SuggestedRulesListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 200 "..\..\..\MainWindow.xaml"
            this.SuggestedRulesListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SuggestedRulesListBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 31:
            this.ApplySuggestedRuleButton = ((System.Windows.Controls.Button)(target));
            
            #line 204 "..\..\..\MainWindow.xaml"
            this.ApplySuggestedRuleButton.Click += new System.Windows.RoutedEventHandler(this.ApplySuggestedRule_Click);
            
            #line default
            #line hidden
            return;
            case 32:
            this.RuleNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 33:
            this.RuleCommentTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 34:
            this.ConditionsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 35:
            this.AddConditionButton = ((System.Windows.Controls.Button)(target));
            
            #line 228 "..\..\..\MainWindow.xaml"
            this.AddConditionButton.Click += new System.Windows.RoutedEventHandler(this.AddCondition_Click);
            
            #line default
            #line hidden
            return;
            case 36:
            this.LogicalOperatorComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 37:
            this.TestRuleButton = ((System.Windows.Controls.Button)(target));
            
            #line 242 "..\..\..\MainWindow.xaml"
            this.TestRuleButton.Click += new System.Windows.RoutedEventHandler(this.TestRule_Click);
            
            #line default
            #line hidden
            return;
            case 38:
            this.SaveRuleButton = ((System.Windows.Controls.Button)(target));
            
            #line 247 "..\..\..\MainWindow.xaml"
            this.SaveRuleButton.Click += new System.Windows.RoutedEventHandler(this.SaveRule_Click);
            
            #line default
            #line hidden
            return;
            case 39:
            this.PreviousRowButton = ((System.Windows.Controls.Button)(target));
            
            #line 261 "..\..\..\MainWindow.xaml"
            this.PreviousRowButton.Click += new System.Windows.RoutedEventHandler(this.PreviousRow_Click);
            
            #line default
            #line hidden
            return;
            case 40:
            this.NextRowButton = ((System.Windows.Controls.Button)(target));
            
            #line 267 "..\..\..\MainWindow.xaml"
            this.NextRowButton.Click += new System.Windows.RoutedEventHandler(this.NextRow_Click);
            
            #line default
            #line hidden
            return;
            case 41:
            this.SkipRowButton = ((System.Windows.Controls.Button)(target));
            
            #line 276 "..\..\..\MainWindow.xaml"
            this.SkipRowButton.Click += new System.Windows.RoutedEventHandler(this.SkipRow_Click);
            
            #line default
            #line hidden
            return;
            case 42:
            this.UndoButton = ((System.Windows.Controls.Button)(target));
            
            #line 282 "..\..\..\MainWindow.xaml"
            this.UndoButton.Click += new System.Windows.RoutedEventHandler(this.Undo_Click);
            
            #line default
            #line hidden
            return;
            case 43:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 44:
            this.ProgressText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 45:
            this.ProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

