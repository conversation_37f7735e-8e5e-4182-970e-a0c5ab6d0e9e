<Window x:Class="Accountinghelper.Views.RuleCreationDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Create/Edit Rule" Height="700" Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">
    
    <Window.Resources>
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>
        
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,5,0,2"/>
        </Style>
        
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="MinWidth" Value="100"/>
        </Style>
    </Window.Resources>

    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" Name="HeaderText" Text="Create New Rule" Style="{StaticResource HeaderTextStyle}"/>

        <!-- Main Content -->
        <TabControl Grid.Row="1" Margin="0,10">
            
            <!-- Natural Language Tab -->
            <TabItem Header="🗣️ Natural Language" ToolTip="Create rules using plain English descriptions">
                <ScrollViewer>
                    <StackPanel Margin="10">
                        <!-- Header with helpful description -->
                        <TextBlock Text="Create a rule using plain English"
                                   Style="{StaticResource LabelStyle}"
                                   FontSize="16"
                                   FontWeight="Bold"/>
                        <TextBlock Text="Simply describe what transactions should be classified and how. The system will automatically understand your intent."
                                   Margin="0,0,0,15"
                                   FontStyle="Italic"
                                   Foreground="Gray"
                                   TextWrapping="Wrap"/>

                        <!-- Input Section -->
                        <TextBlock Text="Describe your rule:" Style="{StaticResource LabelStyle}"/>
                        <TextBox Name="NaturalLanguageTextBox"
                                 Height="100"
                                 TextWrapping="Wrap"
                                 AcceptsReturn="True"
                                 VerticalScrollBarVisibility="Auto"
                                 TextChanged="NaturalLanguageTextBox_TextChanged"
                                 ToolTip="Type your rule in plain English. For example:&#x0a;• 'transactions containing Manish should be PankajJi Labour'&#x0a;• 'amounts greater than 1000 should be Large Expense'&#x0a;• 'description contains SALARY should be Income'"
                                 Margin="0,5,0,10"/>

                        <!-- Examples Section -->
                        <Expander Header="💡 Examples &amp; Tips" Margin="0,0,0,15" IsExpanded="True">
                            <StackPanel Margin="10,5">
                                <TextBlock Text="✅ Good examples:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBlock Text="• transactions containing Manish should be PankajJi Labour" Margin="10,2" Foreground="DarkGreen"/>
                                <TextBlock Text="• amounts greater than 1000 should be Large Expense" Margin="10,2" Foreground="DarkGreen"/>
                                <TextBlock Text="• description contains SALARY should be Income" Margin="10,2" Foreground="DarkGreen"/>
                                <TextBlock Text="• reference starts with UPI should be Digital Payment" Margin="10,2" Foreground="DarkGreen"/>

                                <TextBlock Text="💡 Tips:" FontWeight="Bold" Margin="0,10,0,5"/>
                                <TextBlock Text="• Use 'contains', 'starts with', 'ends with' for text matching" Margin="10,2"/>
                                <TextBlock Text="• Use 'greater than', 'less than', '>', '<' for amounts" Margin="10,2"/>
                                <TextBlock Text="• Always end with 'should be [classification]'" Margin="10,2"/>
                                <TextBlock Text="• Be specific but natural - write as you would speak" Margin="10,2"/>
                            </StackPanel>
                        </Expander>

                        <!-- Action Button -->
                        <Button Name="ParseNaturalLanguageButton"
                                Content="✨ Create Rule from Description"
                                Style="{StaticResource ButtonStyle}"
                                Click="ParseNaturalLanguage_Click"
                                Margin="0,10,0,0"
                                HorizontalAlignment="Left"
                                Padding="15,8"
                                Background="LightGreen"
                                ToolTip="Parse your description and create a rule automatically"/>

                        <!-- Feedback Section -->
                        <TextBlock Name="ParseResultText"
                                   Margin="0,15,0,10"
                                   TextWrapping="Wrap"
                                   Foreground="Green"
                                   FontWeight="Bold"/>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
            
            <!-- Visual Builder Tab -->
            <TabItem Header="🔧 Visual Builder" ToolTip="Build rules step-by-step with visual controls">
                <ScrollViewer>
                    <StackPanel Margin="10">

                        <!-- Header -->
                        <TextBlock Text="Build your rule step by step"
                                   Style="{StaticResource LabelStyle}"
                                   FontSize="16"
                                   FontWeight="Bold"/>
                        <TextBlock Text="Use the visual controls below to create precise rules with multiple conditions."
                                   Margin="0,0,0,15"
                                   FontStyle="Italic"
                                   Foreground="Gray"
                                   TextWrapping="Wrap"/>

                        <!-- Quick Templates -->
                        <GroupBox Header="🚀 Quick Templates" Margin="0,0,0,15">
                            <StackPanel>
                                <TextBlock Text="Click a template to quickly create common rules:"
                                           FontStyle="Italic"
                                           Margin="0,0,0,10"/>
                                <WrapPanel>
                                    <Button Name="MaheshSalaryTemplateButton"
                                            Content="💰 Mahesh Salary"
                                            Margin="0,0,10,5"
                                            Padding="10,5"
                                            Click="MaheshSalaryTemplateButton_Click"
                                            Background="LightBlue"
                                            ToolTip="Create a rule specifically for Mahesh's salary transactions"/>
                                    <Button Name="SalaryTemplateButton"
                                            Content="💵 General Salary"
                                            Margin="0,0,10,5"
                                            Padding="10,5"
                                            Click="SalaryTemplateButton_Click"
                                            ToolTip="Create a rule for general salary transactions"/>
                                    <Button Name="ATMTemplateButton"
                                            Content="🏧 ATM Withdrawal"
                                            Margin="0,0,10,5"
                                            Padding="10,5"
                                            Click="ATMTemplateButton_Click"
                                            ToolTip="Create a rule for ATM withdrawal transactions"/>
                                    <Button Name="TransferTemplateButton"
                                            Content="🔄 Bank Transfer"
                                            Margin="0,0,10,5"
                                            Padding="10,5"
                                            Click="TransferTemplateButton_Click"
                                            ToolTip="Create a rule for bank transfer transactions"/>
                                    <Button Name="InterestTemplateButton"
                                            Content="💎 Interest Credit"
                                            Margin="0,0,10,5"
                                            Padding="10,5"
                                            Click="InterestTemplateButton_Click"
                                            ToolTip="Create a rule for interest credit transactions"/>
                                </WrapPanel>
                            </StackPanel>
                        </GroupBox>

                        <!-- Basic Rule Info -->
                        <GroupBox Header="📝 Rule Information" Margin="0,0,0,15">
                            <StackPanel Margin="10">
                                <TextBlock Text="Rule Name (Auto-generated if empty):" Style="{StaticResource LabelStyle}"/>
                                <TextBox Name="RuleNameTextBox"
                                         Margin="0,0,0,10"
                                         ToolTip="Leave empty to auto-generate a name based on conditions, or enter a custom name"/>

                                <TextBlock Text="Classification Comment:" Style="{StaticResource LabelStyle}"/>
                                <TextBox Name="RuleCommentTextBox"
                                         Margin="0,0,0,10"
                                         ToolTip="What should matching transactions be classified as? (e.g., 'Salary', 'ATM Withdrawal', 'Rent')"/>

                                <TextBlock Text="Description (Optional):" Style="{StaticResource LabelStyle}"/>
                                <TextBox Name="RuleDescriptionTextBox"
                                         Margin="0,0,0,10"
                                         ToolTip="Optional description to help you remember what this rule does"/>

                                <TextBlock Text="Priority (Higher = Applied First):" Style="{StaticResource LabelStyle}"/>
                                <TextBox Name="RulePriorityTextBox"
                                         Text="0"
                                         Margin="0,0,0,10"
                                         ToolTip="Rules with higher priority numbers are applied first. Use this to control rule order when multiple rules might match."/>
                            </StackPanel>
                        </GroupBox>
                        
                        <!-- Conditions -->
                        <GroupBox Header="🎯 Conditions" Margin="0,0,0,15">
                            <StackPanel Margin="10">
                                <TextBlock Text="Define when this rule should apply:"
                                           FontStyle="Italic"
                                           Foreground="Gray"
                                           Margin="0,0,0,10"/>

                                <StackPanel Name="ConditionsPanel" Margin="0,5,0,10">
                                    <!-- Dynamic conditions will be added here -->
                                </StackPanel>

                                <Button Name="AddConditionButton"
                                        Content="➕ Add Condition"
                                        Style="{StaticResource ButtonStyle}"
                                        Click="AddCondition_Click"
                                        HorizontalAlignment="Left"
                                        Padding="10,5"
                                        ToolTip="Add a new condition to this rule. You can have multiple conditions."/>

                                <TextBlock Text="Logical Operator (for multiple conditions):"
                                           Style="{StaticResource LabelStyle}"
                                           Margin="0,15,0,5"/>
                                <ComboBox Name="LogicalOperatorComboBox"
                                          Margin="0,0,0,10"
                                          SelectedIndex="0"
                                          ToolTip="Choose how multiple conditions should be combined:&#x0a;• AND: All conditions must match&#x0a;• OR: Any condition can match">
                                    <ComboBoxItem Content="AND (All conditions must match)" ToolTip="Transaction must match ALL conditions to trigger this rule"/>
                                    <ComboBoxItem Content="OR (Any condition can match)" ToolTip="Transaction needs to match ANY ONE condition to trigger this rule"/>
                                </ComboBox>
                            </StackPanel>
                        </GroupBox>
                        
                        <!-- Preview -->
                        <TextBlock Text="Rule Preview:" Style="{StaticResource LabelStyle}"/>
                        <Border BorderBrush="Gray" BorderThickness="1" Padding="10" Margin="0,5,0,10">
                            <TextBlock Name="RulePreviewText" 
                                       Text="No conditions defined" 
                                       TextWrapping="Wrap"
                                       FontFamily="Consolas"/>
                        </Border>
                        
                        <!-- Test Results -->
                        <TextBlock Name="TestResultsHeader" 
                                   Text="Test Results:" 
                                   Style="{StaticResource LabelStyle}"
                                   Visibility="Collapsed"/>
                        <TextBlock Name="TestResultsText" 
                                   Margin="0,5,0,10"
                                   TextWrapping="Wrap"
                                   Visibility="Collapsed"/>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,10,0,0">
            <Button Name="TestRuleButton" Content="Test Rule" Style="{StaticResource ButtonStyle}" Click="TestRule_Click"/>
            <Button Name="SaveRuleButton" Content="Save Rule" Style="{StaticResource ButtonStyle}" Click="SaveRule_Click"/>
            <Button Name="CancelButton" Content="Cancel" Style="{StaticResource ButtonStyle}" Click="Cancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
