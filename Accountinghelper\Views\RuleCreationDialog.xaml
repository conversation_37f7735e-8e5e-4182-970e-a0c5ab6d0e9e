<Window x:Class="Accountinghelper.Views.RuleCreationDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Create/Edit Rule" Height="700" Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">
    
    <Window.Resources>
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>
        
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,5,0,2"/>
        </Style>
        
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="MinWidth" Value="100"/>
        </Style>
    </Window.Resources>

    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" Name="HeaderText" Text="Create New Rule" Style="{StaticResource HeaderTextStyle}"/>

        <!-- Main Content -->
        <TabControl Grid.Row="1" Margin="0,10">
            
            <!-- Natural Language Tab -->
            <TabItem Header="Natural Language">
                <ScrollViewer>
                    <StackPanel Margin="10">
                        <TextBlock Text="Describe your rule in plain English:" Style="{StaticResource LabelStyle}"/>
                        <TextBlock Text="Examples: 'transactions containing Manish should be PankajJi Labour' or 'amounts greater than 1000 should be Large Expense'" 
                                   Margin="0,0,0,10" FontStyle="Italic" Foreground="Gray"/>
                        
                        <TextBox Name="NaturalLanguageTextBox" 
                                 Height="80" 
                                 TextWrapping="Wrap" 
                                 AcceptsReturn="True"
                                 VerticalScrollBarVisibility="Auto"
                                 TextChanged="NaturalLanguageTextBox_TextChanged"/>
                        
                        <Button Name="ParseNaturalLanguageButton" 
                                Content="Parse Rule" 
                                Style="{StaticResource ButtonStyle}"
                                Click="ParseNaturalLanguage_Click"
                                Margin="0,10,0,0"
                                HorizontalAlignment="Left"/>
                        
                        <TextBlock Name="ParseResultText" 
                                   Margin="0,10" 
                                   TextWrapping="Wrap"
                                   Foreground="Green"/>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
            
            <!-- Visual Builder Tab -->
            <TabItem Header="Visual Builder">
                <ScrollViewer>
                    <StackPanel Margin="10">

                        <!-- Quick Templates -->
                        <GroupBox Header="Quick Templates" Margin="0,0,0,15">
                            <StackPanel>
                                <TextBlock Text="Click a template to quickly create common rules:" FontStyle="Italic" Margin="0,0,0,10"/>
                                <WrapPanel>
                                    <Button Name="MaheshSalaryTemplateButton" Content="Mahesh Salary" Margin="0,0,10,5" Padding="10,5" Click="MaheshSalaryTemplateButton_Click" Background="LightBlue"/>
                                    <Button Name="SalaryTemplateButton" Content="General Salary" Margin="0,0,10,5" Padding="10,5" Click="SalaryTemplateButton_Click"/>
                                    <Button Name="ATMTemplateButton" Content="ATM Withdrawal" Margin="0,0,10,5" Padding="10,5" Click="ATMTemplateButton_Click"/>
                                    <Button Name="TransferTemplateButton" Content="Bank Transfer" Margin="0,0,10,5" Padding="10,5" Click="TransferTemplateButton_Click"/>
                                    <Button Name="InterestTemplateButton" Content="Interest Credit" Margin="0,0,10,5" Padding="10,5" Click="InterestTemplateButton_Click"/>
                                </WrapPanel>
                            </StackPanel>
                        </GroupBox>

                        <!-- Basic Rule Info -->
                        <TextBlock Text="Rule Name:" Style="{StaticResource LabelStyle}"/>
                        <TextBox Name="RuleNameTextBox" Margin="0,0,0,10"/>

                        <TextBlock Text="Classification Comment:" Style="{StaticResource LabelStyle}"/>
                        <TextBox Name="RuleCommentTextBox" Margin="0,0,0,10"/>
                        
                        <TextBlock Text="Description (Optional):" Style="{StaticResource LabelStyle}"/>
                        <TextBox Name="RuleDescriptionTextBox" Margin="0,0,0,10"/>
                        
                        <TextBlock Text="Priority (Higher = Applied First):" Style="{StaticResource LabelStyle}"/>
                        <TextBox Name="RulePriorityTextBox" Text="0" Margin="0,0,0,10"/>
                        
                        <!-- Conditions -->
                        <TextBlock Text="Conditions:" Style="{StaticResource LabelStyle}"/>
                        
                        <StackPanel Name="ConditionsPanel" Margin="0,5,0,10">
                            <!-- Dynamic conditions will be added here -->
                        </StackPanel>
                        
                        <Button Name="AddConditionButton" 
                                Content="+ Add Condition" 
                                Style="{StaticResource ButtonStyle}"
                                Click="AddCondition_Click"
                                HorizontalAlignment="Left"/>
                        
                        <TextBlock Text="Logical Operator:" Style="{StaticResource LabelStyle}" Margin="0,10,0,2"/>
                        <ComboBox Name="LogicalOperatorComboBox" 
                                  Margin="0,0,0,10"
                                  SelectedIndex="0">
                            <ComboBoxItem Content="AND (All conditions must match)"/>
                            <ComboBoxItem Content="OR (Any condition can match)"/>
                        </ComboBox>
                        
                        <!-- Preview -->
                        <TextBlock Text="Rule Preview:" Style="{StaticResource LabelStyle}"/>
                        <Border BorderBrush="Gray" BorderThickness="1" Padding="10" Margin="0,5,0,10">
                            <TextBlock Name="RulePreviewText" 
                                       Text="No conditions defined" 
                                       TextWrapping="Wrap"
                                       FontFamily="Consolas"/>
                        </Border>
                        
                        <!-- Test Results -->
                        <TextBlock Name="TestResultsHeader" 
                                   Text="Test Results:" 
                                   Style="{StaticResource LabelStyle}"
                                   Visibility="Collapsed"/>
                        <TextBlock Name="TestResultsText" 
                                   Margin="0,5,0,10"
                                   TextWrapping="Wrap"
                                   Visibility="Collapsed"/>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,10,0,0">
            <Button Name="TestRuleButton" Content="Test Rule" Style="{StaticResource ButtonStyle}" Click="TestRule_Click"/>
            <Button Name="SaveRuleButton" Content="Save Rule" Style="{StaticResource ButtonStyle}" Click="SaveRule_Click"/>
            <Button Name="CancelButton" Content="Cancel" Style="{StaticResource ButtonStyle}" Click="Cancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
