using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using Accountinghelper.Models;
using Accountinghelper.Services;

namespace Accountinghelper.Views
{
    public partial class RuleTestDialog : Window
    {
        private readonly ClassificationRule _rule;
        private readonly List<BankTransaction>? _transactionData;
        private readonly RuleEngine? _ruleEngine;
        private List<BankTransaction> _matchingTransactions = new();

        public RuleTestDialog(ClassificationRule rule, List<BankTransaction>? transactionData = null)
        {
            InitializeComponent();
            _rule = rule;
            _transactionData = transactionData;

            // Create a rule engine with just this rule for testing
            if (_transactionData != null)
            {
                _ruleEngine = new RuleEngine(new List<ClassificationRule> { _rule });
            }

            LoadRuleInfo();
            TestRule();
        }

        private void LoadRuleInfo()
        {
            RuleNameText.Text = $"Rule: {_rule.Name}";

            var conditionsText = "Conditions: ";
            if (_rule.Conditions.Any())
            {
                var conditionStrings = _rule.Conditions.Select(c => c.ToString());
                conditionsText += string.Join($" {_rule.LogicalOperator} ", conditionStrings);
            }
            else
            {
                conditionsText += "No conditions defined";
            }

            RuleConditionsText.Text = conditionsText;
        }

        private void TestRule()
        {
            if (_transactionData == null || _ruleEngine == null)
            {
                TestSummaryText.Text = "No transaction data available for testing. Load a bank statement file first.";
                SampleTransactionsDataGrid.ItemsSource = null;
                return;
            }

            try
            {
                // Find all matching transactions
                _matchingTransactions.Clear();

                foreach (var transaction in _transactionData)
                {
                    var matches = _ruleEngine.FindAllMatchingRules(transaction);
                    if (matches.Any(m => m.Rule.Id == _rule.Id))
                    {
                        _matchingTransactions.Add(transaction);
                    }
                }

                // Update summary
                var totalTransactions = _transactionData.Count;
                var matchingCount = _matchingTransactions.Count;
                var percentage = totalTransactions > 0 ? (double)matchingCount / totalTransactions * 100 : 0;

                TestSummaryText.Text = $"Found {matchingCount} matching transactions out of {totalTransactions} total ({percentage:F1}%)";

                // Show sample matching transactions (limit to first 100 for performance)
                var sampleTransactions = _matchingTransactions.Take(100).ToList();
                SampleTransactionsDataGrid.ItemsSource = sampleTransactions;

                // Update window title to show results
                Title = $"Test Rule - {matchingCount} matches found";

                // Enable Apply button if there are matches and the rule has a comment
                ApplyToAllButton.IsEnabled = matchingCount > 0 && !string.IsNullOrWhiteSpace(_rule.Comment);
            }
            catch (Exception ex)
            {
                TestSummaryText.Text = $"Error testing rule: {ex.Message}";
                SampleTransactionsDataGrid.ItemsSource = null;
                ApplyToAllButton.IsEnabled = false;
            }
        }

        private void ApplyToAll_Click(object sender, RoutedEventArgs e)
        {
            if (_matchingTransactions.Count == 0)
            {
                MessageBox.Show("No matching transactions to apply the rule to.", "No Matches",
                    MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var result = MessageBox.Show(
                $"This will apply the rule '{_rule.Name}' to {_matchingTransactions.Count} matching transactions.\n\n" +
                $"Comment to apply: '{_rule.Comment}'\n\n" +
                "Are you sure you want to continue?",
                "Apply Rule to All Matches",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    int appliedCount = 0;
                    foreach (var transaction in _matchingTransactions)
                    {
                        // Only apply if the transaction doesn't already have a comment
                        if (string.IsNullOrWhiteSpace(transaction.MyComments))
                        {
                            transaction.MyComments = _rule.Comment;
                            transaction.IsRuleApplied = true;
                            appliedCount++;
                        }
                    }

                    // Update rule usage statistics
                    _rule.IncrementUsage();

                    MessageBox.Show(
                        $"Successfully applied rule to {appliedCount} transactions.\n" +
                        $"({_matchingTransactions.Count - appliedCount} transactions already had comments and were skipped.)",
                        "Rule Applied",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);

                    // Refresh the display to show updated comments
                    SampleTransactionsDataGrid.Items.Refresh();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error applying rule: {ex.Message}", "Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
