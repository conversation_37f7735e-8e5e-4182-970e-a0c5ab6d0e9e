﻿#pragma checksum "..\..\..\..\Views\RuleManagementDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "39E2992856A01F7964A6B33E4658071C11E0AA62"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace Accountinghelper.Views {
    
    
    /// <summary>
    /// RuleManagementDialog
    /// </summary>
    public partial class RuleManagementDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 35 "..\..\..\..\Views\RuleManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid RulesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\Views\RuleManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel RuleDetailsPanel;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\..\Views\RuleManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RuleNameText;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\Views\RuleManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RuleDescriptionText;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\Views\RuleManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RuleConditionsText;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\..\Views\RuleManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NewRuleButton;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\Views\RuleManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditRuleButton;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\Views\RuleManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteRuleButton;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\Views\RuleManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestRuleButton;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\Views\RuleManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportRulesButton;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\Views\RuleManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ImportRulesButton;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\..\Views\RuleManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/Accountinghelper;component/views/rulemanagementdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\RuleManagementDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.RulesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 40 "..\..\..\..\Views\RuleManagementDialog.xaml"
            this.RulesDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.RulesDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.RuleDetailsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 3:
            this.RuleNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.RuleDescriptionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.RuleConditionsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.NewRuleButton = ((System.Windows.Controls.Button)(target));
            
            #line 67 "..\..\..\..\Views\RuleManagementDialog.xaml"
            this.NewRuleButton.Click += new System.Windows.RoutedEventHandler(this.NewRule_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.EditRuleButton = ((System.Windows.Controls.Button)(target));
            
            #line 68 "..\..\..\..\Views\RuleManagementDialog.xaml"
            this.EditRuleButton.Click += new System.Windows.RoutedEventHandler(this.EditRule_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.DeleteRuleButton = ((System.Windows.Controls.Button)(target));
            
            #line 69 "..\..\..\..\Views\RuleManagementDialog.xaml"
            this.DeleteRuleButton.Click += new System.Windows.RoutedEventHandler(this.DeleteRule_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.TestRuleButton = ((System.Windows.Controls.Button)(target));
            
            #line 70 "..\..\..\..\Views\RuleManagementDialog.xaml"
            this.TestRuleButton.Click += new System.Windows.RoutedEventHandler(this.TestRule_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.ExportRulesButton = ((System.Windows.Controls.Button)(target));
            
            #line 71 "..\..\..\..\Views\RuleManagementDialog.xaml"
            this.ExportRulesButton.Click += new System.Windows.RoutedEventHandler(this.ExportRules_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.ImportRulesButton = ((System.Windows.Controls.Button)(target));
            
            #line 72 "..\..\..\..\Views\RuleManagementDialog.xaml"
            this.ImportRulesButton.Click += new System.Windows.RoutedEventHandler(this.ImportRules_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 73 "..\..\..\..\Views\RuleManagementDialog.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.Close_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

