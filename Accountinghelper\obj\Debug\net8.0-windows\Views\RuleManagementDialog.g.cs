﻿#pragma checksum "..\..\..\..\Views\RuleManagementDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "1C3A71E70283B75BCEE0E8E63B687DE286002F80"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace Accountinghelper.Views {
    
    
    /// <summary>
    /// RuleManagementDialog
    /// </summary>
    public partial class RuleManagementDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 42 "..\..\..\..\Views\RuleManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid RulesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\..\Views\RuleManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel RuleDetailsPanel;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\Views\RuleManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RuleNameText;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\Views\RuleManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RuleDescriptionText;
        
        #line default
        #line hidden
        
        
        #line 151 "..\..\..\..\Views\RuleManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RuleConditionsText;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\..\Views\RuleManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveChangesButton;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\..\Views\RuleManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetOrderButton;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\..\Views\RuleManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NewRuleButton;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\..\Views\RuleManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditRuleButton;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\..\Views\RuleManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DuplicateRuleButton;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\..\Views\RuleManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteRuleButton;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\..\Views\RuleManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestRuleButton;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\..\Views\RuleManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportRulesButton;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\..\Views\RuleManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ImportRulesButton;
        
        #line default
        #line hidden
        
        
        #line 221 "..\..\..\..\Views\RuleManagementDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/Accountinghelper;component/views/rulemanagementdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\RuleManagementDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.RulesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 47 "..\..\..\..\Views\RuleManagementDialog.xaml"
            this.RulesDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.RulesDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            
            #line 49 "..\..\..\..\Views\RuleManagementDialog.xaml"
            this.RulesDataGrid.Drop += new System.Windows.DragEventHandler(this.RulesDataGrid_Drop);
            
            #line default
            #line hidden
            
            #line 50 "..\..\..\..\Views\RuleManagementDialog.xaml"
            this.RulesDataGrid.DragOver += new System.Windows.DragEventHandler(this.RulesDataGrid_DragOver);
            
            #line default
            #line hidden
            
            #line 51 "..\..\..\..\Views\RuleManagementDialog.xaml"
            this.RulesDataGrid.PreviewMouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.RulesDataGrid_PreviewMouseLeftButtonDown);
            
            #line default
            #line hidden
            
            #line 52 "..\..\..\..\Views\RuleManagementDialog.xaml"
            this.RulesDataGrid.MouseMove += new System.Windows.Input.MouseEventHandler(this.RulesDataGrid_MouseMove);
            
            #line default
            #line hidden
            
            #line 53 "..\..\..\..\Views\RuleManagementDialog.xaml"
            this.RulesDataGrid.CellEditEnding += new System.EventHandler<System.Windows.Controls.DataGridCellEditEndingEventArgs>(this.RulesDataGrid_CellEditEnding);
            
            #line default
            #line hidden
            
            #line 54 "..\..\..\..\Views\RuleManagementDialog.xaml"
            this.RulesDataGrid.BeginningEdit += new System.EventHandler<System.Windows.Controls.DataGridBeginningEditEventArgs>(this.RulesDataGrid_BeginningEdit);
            
            #line default
            #line hidden
            return;
            case 2:
            this.RuleDetailsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 3:
            this.RuleNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.RuleDescriptionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.RuleConditionsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.SaveChangesButton = ((System.Windows.Controls.Button)(target));
            
            #line 168 "..\..\..\..\Views\RuleManagementDialog.xaml"
            this.SaveChangesButton.Click += new System.Windows.RoutedEventHandler(this.SaveChanges_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ResetOrderButton = ((System.Windows.Controls.Button)(target));
            
            #line 175 "..\..\..\..\Views\RuleManagementDialog.xaml"
            this.ResetOrderButton.Click += new System.Windows.RoutedEventHandler(this.ResetOrder_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.NewRuleButton = ((System.Windows.Controls.Button)(target));
            
            #line 184 "..\..\..\..\Views\RuleManagementDialog.xaml"
            this.NewRuleButton.Click += new System.Windows.RoutedEventHandler(this.NewRule_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.EditRuleButton = ((System.Windows.Controls.Button)(target));
            
            #line 189 "..\..\..\..\Views\RuleManagementDialog.xaml"
            this.EditRuleButton.Click += new System.Windows.RoutedEventHandler(this.EditRule_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.DuplicateRuleButton = ((System.Windows.Controls.Button)(target));
            
            #line 195 "..\..\..\..\Views\RuleManagementDialog.xaml"
            this.DuplicateRuleButton.Click += new System.Windows.RoutedEventHandler(this.DuplicateRule_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.DeleteRuleButton = ((System.Windows.Controls.Button)(target));
            
            #line 201 "..\..\..\..\Views\RuleManagementDialog.xaml"
            this.DeleteRuleButton.Click += new System.Windows.RoutedEventHandler(this.DeleteRule_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.TestRuleButton = ((System.Windows.Controls.Button)(target));
            
            #line 207 "..\..\..\..\Views\RuleManagementDialog.xaml"
            this.TestRuleButton.Click += new System.Windows.RoutedEventHandler(this.TestRule_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.ExportRulesButton = ((System.Windows.Controls.Button)(target));
            
            #line 214 "..\..\..\..\Views\RuleManagementDialog.xaml"
            this.ExportRulesButton.Click += new System.Windows.RoutedEventHandler(this.ExportRules_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.ImportRulesButton = ((System.Windows.Controls.Button)(target));
            
            #line 219 "..\..\..\..\Views\RuleManagementDialog.xaml"
            this.ImportRulesButton.Click += new System.Windows.RoutedEventHandler(this.ImportRules_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 224 "..\..\..\..\Views\RuleManagementDialog.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.Close_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

