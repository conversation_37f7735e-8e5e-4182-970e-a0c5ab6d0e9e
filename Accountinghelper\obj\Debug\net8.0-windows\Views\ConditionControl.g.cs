﻿#pragma checksum "..\..\..\..\Views\ConditionControl.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "567400F6A50DE7F45D7C6B1CE1A6F79F034ECE42"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace Accountinghelper.Views {
    
    
    /// <summary>
    /// ConditionControl
    /// </summary>
    public partial class ConditionControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 45 "..\..\..\..\Views\ConditionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ColumnComboBox;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\Views\ConditionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox OperatorComboBox;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\..\Views\ConditionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ValueComboBox;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\Views\ConditionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RemoveButton;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\Views\ConditionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ValidationMessageText;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\Views\ConditionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PreviewText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/Accountinghelper;component/views/conditioncontrol.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ConditionControl.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ColumnComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 49 "..\..\..\..\Views\ConditionControl.xaml"
            this.ColumnComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ColumnComboBox_SelectionChanged);
            
            #line default
            #line hidden
            
            #line 50 "..\..\..\..\Views\ConditionControl.xaml"
            this.ColumnComboBox.LostFocus += new System.Windows.RoutedEventHandler(this.ColumnComboBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 2:
            this.OperatorComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 58 "..\..\..\..\Views\ConditionControl.xaml"
            this.OperatorComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.OperatorComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ValueComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 68 "..\..\..\..\Views\ConditionControl.xaml"
            this.ValueComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ValueComboBox_SelectionChanged);
            
            #line default
            #line hidden
            
            #line 69 "..\..\..\..\Views\ConditionControl.xaml"
            this.ValueComboBox.LostFocus += new System.Windows.RoutedEventHandler(this.ValueComboBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 4:
            this.RemoveButton = ((System.Windows.Controls.Button)(target));
            
            #line 80 "..\..\..\..\Views\ConditionControl.xaml"
            this.RemoveButton.Click += new System.Windows.RoutedEventHandler(this.RemoveButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ValidationMessageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.PreviewText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

