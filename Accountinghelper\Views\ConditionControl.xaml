<UserControl x:Class="Accountinghelper.Views.ConditionControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <UserControl.Resources>
        <!-- Validation State Styles -->
        <Style x:Key="ValidColumnStyle" TargetType="ComboBox">
            <Setter Property="Background" Value="LightGreen"/>
            <Setter Property="BorderBrush" Value="Green"/>
            <Setter Property="BorderThickness" Value="2"/>
        </Style>

        <Style x:Key="InvalidColumnStyle" TargetType="ComboBox">
            <Setter Property="Background" Value="LightCoral"/>
            <Setter Property="BorderBrush" Value="Red"/>
            <Setter Property="BorderThickness" Value="2"/>
        </Style>

        <Style x:Key="CorrectedColumnStyle" TargetType="ComboBox">
            <Setter Property="Background" Value="LightYellow"/>
            <Setter Property="BorderBrush" Value="Orange"/>
            <Setter Property="BorderThickness" Value="2"/>
        </Style>
    </UserControl.Resources>

    <Border BorderBrush="LightGray" BorderThickness="1" Padding="10" Margin="0,5">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Main Condition Row -->
            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="180"/>
                    <ColumnDefinition Width="140"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Smart Column Selection with Auto-complete -->
                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                    <TextBlock Text="Column:" FontWeight="Bold" FontSize="10" Margin="0,0,0,2"/>
                    <ComboBox Name="ColumnComboBox"
                              IsEditable="True"
                              IsTextSearchEnabled="True"
                              StaysOpenOnEdit="True"
                              SelectionChanged="ColumnComboBox_SelectionChanged"
                              LostFocus="ColumnComboBox_LostFocus"
                              ToolTip="Type column name or select from dropdown"/>
                </StackPanel>

                <!-- Operator Selection -->
                <StackPanel Grid.Column="1" Margin="0,0,10,0">
                    <TextBlock Text="Operator:" FontWeight="Bold" FontSize="10" Margin="0,0,0,2"/>
                    <ComboBox Name="OperatorComboBox"
                              SelectionChanged="OperatorComboBox_SelectionChanged"/>
                </StackPanel>

                <!-- Value Input with Smart Suggestions -->
                <StackPanel Grid.Column="2" Margin="0,0,10,0">
                    <TextBlock Text="Value:" FontWeight="Bold" FontSize="10" Margin="0,0,0,2"/>
                    <ComboBox Name="ValueComboBox"
                              IsEditable="True"
                              IsTextSearchEnabled="True"
                              StaysOpenOnEdit="True"
                              SelectionChanged="ValueComboBox_SelectionChanged"
                              LostFocus="ValueComboBox_LostFocus"
                              ToolTip="Enter value or select from suggestions"/>
                </StackPanel>

                <!-- Remove Button -->
                <Button Grid.Column="3"
                        Name="RemoveButton"
                        Content="✕"
                        Width="25"
                        Height="25"
                        Background="LightCoral"
                        Click="RemoveButton_Click"
                        ToolTip="Remove this condition"
                        VerticalAlignment="Bottom"/>
            </Grid>

            <!-- Validation Feedback Row -->
            <StackPanel Grid.Row="1" Margin="0,5,0,0">
                <TextBlock Name="ValidationMessageText"
                           FontSize="10"
                           TextWrapping="Wrap"
                           Visibility="Collapsed"/>
                <TextBlock Name="PreviewText"
                           FontSize="10"
                           FontStyle="Italic"
                           Foreground="Gray"
                           TextWrapping="Wrap"
                           Visibility="Collapsed"/>
            </StackPanel>
        </Grid>
    </Border>
</UserControl>
