using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using Microsoft.Win32;
using Accountinghelper.Models;
using Accountinghelper.Services;

namespace Accountinghelper
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private ObservableCollection<BankTransaction> _transactions = new();
        private ExcelDataService? _excelService;
        private RuleManager _ruleManager = null!;
        private RuleEngine? _ruleEngine;
        private PatternAnalyzer _patternAnalyzer = null!;
        private int _currentRowIndex = -1;
        private bool _isProcessing = false;
        private Stack<UndoAction> _undoStack = new();

        public MainWindow()
        {
            InitializeComponent();
            InitializeServices();
            SetupKeyboardShortcuts();
            InitializeToastNotifications();
            UpdateUI();
        }

        private void InitializeToastNotifications()
        {
            ToastNotificationService.Instance.Initialize(ToastContainer);
        }

        private void InitializeServices()
        {
            _ruleManager = new RuleManager();
            _patternAnalyzer = new PatternAnalyzer();
            UpdateRuleEngine();
        }

        private void UpdateRuleEngine()
        {
            var rules = _ruleManager.GetAllRules();
            _ruleEngine = new RuleEngine(rules);
        }

        private void SetupKeyboardShortcuts()
        {
            // File operations
            var openCommand = new RoutedCommand();
            openCommand.InputGestures.Add(new KeyGesture(Key.O, ModifierKeys.Control));
            CommandBindings.Add(new CommandBinding(openCommand, (s, e) => OpenBankStatement_Click(s, e)));

            var saveCommand = new RoutedCommand();
            saveCommand.InputGestures.Add(new KeyGesture(Key.S, ModifierKeys.Control));
            CommandBindings.Add(new CommandBinding(saveCommand, (s, e) => SaveFile_Click(s, e)));

            var saveAsCommand = new RoutedCommand();
            saveAsCommand.InputGestures.Add(new KeyGesture(Key.S, ModifierKeys.Control | ModifierKeys.Shift));
            CommandBindings.Add(new CommandBinding(saveAsCommand, (s, e) => SaveAsFile_Click(s, e)));

            // Rule operations
            var newRuleCommand = new RoutedCommand();
            newRuleCommand.InputGestures.Add(new KeyGesture(Key.N, ModifierKeys.Control));
            CommandBindings.Add(new CommandBinding(newRuleCommand, (s, e) => CreateRule_Click(s, e)));

            var manageRulesCommand = new RoutedCommand();
            manageRulesCommand.InputGestures.Add(new KeyGesture(Key.R, ModifierKeys.Control));
            CommandBindings.Add(new CommandBinding(manageRulesCommand, (s, e) => ManageRules_Click(s, e)));

            // Processing operations
            var startProcessingCommand = new RoutedCommand();
            startProcessingCommand.InputGestures.Add(new KeyGesture(Key.F2));
            CommandBindings.Add(new CommandBinding(startProcessingCommand, (s, e) => StartProcessing_Click(s, e)));

            var nextRowCommand = new RoutedCommand();
            nextRowCommand.InputGestures.Add(new KeyGesture(Key.F3));
            CommandBindings.Add(new CommandBinding(nextRowCommand, (s, e) => NextRow_Click(s, e)));

            var previousRowCommand = new RoutedCommand();
            previousRowCommand.InputGestures.Add(new KeyGesture(Key.F1));
            CommandBindings.Add(new CommandBinding(previousRowCommand, (s, e) => PreviousRow_Click(s, e)));

            var skipRowCommand = new RoutedCommand();
            skipRowCommand.InputGestures.Add(new KeyGesture(Key.F4));
            CommandBindings.Add(new CommandBinding(skipRowCommand, (s, e) => SkipRow_Click(s, e)));

            var applyAllRulesCommand = new RoutedCommand();
            applyAllRulesCommand.InputGestures.Add(new KeyGesture(Key.F5));
            CommandBindings.Add(new CommandBinding(applyAllRulesCommand, (s, e) => ApplyAllRules_Click(s, e)));
        }

        private void UpdateUI()
        {
            bool hasData = _transactions.Count > 0;
            bool hasSelection = _currentRowIndex >= 0 && _currentRowIndex < _transactions.Count;

            // Enable/disable buttons based on state
            SaveButton.IsEnabled = hasData;
            CreateRuleButton.IsEnabled = hasData;
            StartProcessingButton.IsEnabled = hasData && !_isProcessing;
            ApplyAllRulesButton.IsEnabled = hasData;

            ManualEntryButton.IsEnabled = hasSelection;
            ApplyManualCommentButton.IsEnabled = hasSelection && !string.IsNullOrWhiteSpace(ManualCommentTextBox.Text);

            PreviousRowButton.IsEnabled = _currentRowIndex > 0;
            NextRowButton.IsEnabled = _currentRowIndex < _transactions.Count - 1;
            SkipRowButton.IsEnabled = hasSelection;
            UndoButton.IsEnabled = _undoStack.Count > 0;

            // Update status
            if (hasData)
            {
                var processedCount = _transactions.Count(t => !string.IsNullOrWhiteSpace(t.MyComments));
                StatusText.Text = $"Loaded {_transactions.Count} transactions. {processedCount} classified.";
                ProgressText.Text = $"Row {_currentRowIndex + 1} of {_transactions.Count}";
            }
            else
            {
                StatusText.Text = "Ready - Open a bank statement file to begin";
                ProgressText.Text = "";
            }
        }

        #region File Operations

        private void OpenBankStatement_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "Open Bank Statement",
                Filter = "Excel Files (*.xlsx;*.xls)|*.xlsx;*.xls|All Files (*.*)|*.*",
                FilterIndex = 1
            };

            if (openFileDialog.ShowDialog() == true)
            {
                LoadBankStatement(openFileDialog.FileName);
            }
        }

        private void LoadBankStatement(string filePath)
        {
            try
            {
                StatusText.Text = "Loading bank statement...";
                ProgressBar.Visibility = Visibility.Visible;

                _excelService?.Dispose();
                _excelService = new ExcelDataService();

                var transactions = _excelService.LoadExcelFile(filePath);

                _transactions.Clear();
                foreach (var transaction in transactions)
                {
                    _transactions.Add(transaction);
                }

                BankStatementDataGrid.ItemsSource = _transactions;

                // Auto-analyze reference files in the same directory
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory))
                {
                    AnalyzeReferenceFilesInBackground(directory);
                }

                _currentRowIndex = -1;
                Title = $"Bank Statement Classification Tool - {Path.GetFileName(filePath)}";

                StatusText.Text = $"Loaded {_transactions.Count} transactions successfully";
                ProgressBar.Visibility = Visibility.Collapsed;

                // Show success toast
                ToastNotificationService.Instance.ShowSuccess("File loaded successfully", $"Loaded {_transactions.Count} transactions from {Path.GetFileName(filePath)}");

                UpdateUI();
            }
            catch (Exception ex)
            {
                ProgressBar.Visibility = Visibility.Collapsed;
                StatusText.Text = "Error loading file";
                MessageBox.Show($"Error loading bank statement: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveFile_Click(object sender, RoutedEventArgs e)
        {
            if (_excelService == null)
            {
                MessageBox.Show("No file is currently open.", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                StatusText.Text = "Saving file...";
                _excelService.SaveExcelFile(_transactions.ToList());
                StatusText.Text = "File saved successfully";

                // Show success toast
                ToastNotificationService.Instance.ShowSuccess("File saved", "Bank statement saved successfully");
            }
            catch (Exception ex)
            {
                StatusText.Text = "Error saving file";
                MessageBox.Show($"Error saving file: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveAsFile_Click(object sender, RoutedEventArgs e)
        {
            var saveFileDialog = new SaveFileDialog
            {
                Title = "Save Bank Statement As",
                Filter = "Excel Files (*.xlsx)|*.xlsx|All Files (*.*)|*.*",
                FilterIndex = 1
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    StatusText.Text = "Saving file...";
                    _excelService?.SaveExcelFile(_transactions.ToList(), saveFileDialog.FileName);
                    StatusText.Text = "File saved successfully";
                }
                catch (Exception ex)
                {
                    StatusText.Text = "Error saving file";
                    MessageBox.Show($"Error saving file: {ex.Message}", "Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void Exit_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        #endregion

        #region Rule Operations

        private void CreateRule_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new Views.RuleCreationDialog(_ruleManager, null, _transactions.ToList());
            dialog.Owner = this;
            if (dialog.ShowDialog() == true)
            {
                UpdateRuleEngine();
                UpdateSuggestedRules();
            }
        }

        private void ManageRules_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new Views.RuleManagementDialog(_ruleManager, _transactions.ToList());
            dialog.Owner = this;
            dialog.ShowDialog();

            // Refresh rule engine after potential changes
            UpdateRuleEngine();
        }

        private void ImportRules_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "Import Rules",
                Filter = "JSON Files (*.json)|*.json|All Files (*.*)|*.*",
                FilterIndex = 1
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    _ruleManager.ImportRules(openFileDialog.FileName);
                    UpdateRuleEngine();
                    StatusText.Text = "Rules imported successfully";
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error importing rules: {ex.Message}", "Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void ExportRules_Click(object sender, RoutedEventArgs e)
        {
            var saveFileDialog = new SaveFileDialog
            {
                Title = "Export Rules",
                Filter = "JSON Files (*.json)|*.json|All Files (*.*)|*.*",
                FilterIndex = 1,
                FileName = "rules.json"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    _ruleManager.ExportRules(saveFileDialog.FileName);
                    StatusText.Text = "Rules exported successfully";
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error exporting rules: {ex.Message}", "Error",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        #endregion

        #region Processing Operations

        private void StartProcessing_Click(object sender, RoutedEventArgs e)
        {
            if (_transactions.Count == 0) return;

            _isProcessing = true;
            _currentRowIndex = 0;

            // Find first unprocessed row
            for (int i = 0; i < _transactions.Count; i++)
            {
                if (string.IsNullOrWhiteSpace(_transactions[i].MyComments))
                {
                    _currentRowIndex = i;
                    break;
                }
            }

            SetCurrentRow(_currentRowIndex);
            UpdateUI();
        }

        private void NextRow_Click(object sender, RoutedEventArgs e)
        {
            if (_currentRowIndex < _transactions.Count - 1)
            {
                SetCurrentRow(_currentRowIndex + 1);
            }
        }

        private void PreviousRow_Click(object sender, RoutedEventArgs e)
        {
            if (_currentRowIndex > 0)
            {
                SetCurrentRow(_currentRowIndex - 1);
            }
        }

        private void SkipRow_Click(object sender, RoutedEventArgs e)
        {
            NextRow_Click(sender, e);
        }

        private void SetCurrentRow(int rowIndex)
        {
            if (rowIndex < 0 || rowIndex >= _transactions.Count) return;

            // Clear previous current row
            if (_currentRowIndex >= 0 && _currentRowIndex < _transactions.Count)
            {
                _transactions[_currentRowIndex].IsCurrentRow = false;
            }

            _currentRowIndex = rowIndex;
            _transactions[_currentRowIndex].IsCurrentRow = true;

            // Select the row in the DataGrid
            BankStatementDataGrid.SelectedIndex = _currentRowIndex;
            BankStatementDataGrid.ScrollIntoView(_transactions[_currentRowIndex]);

            // Update current row info
            UpdateCurrentRowInfo();

            // Check for matching rules and update suggestions
            UpdateSuggestedRules();

            UpdateUI();
        }

        private void UpdateCurrentRowInfo()
        {
            if (_currentRowIndex < 0 || _currentRowIndex >= _transactions.Count)
            {
                CurrentRowInfo.Text = "No row selected";
                CurrentTransactionDetails.Text = "";
                return;
            }

            var transaction = _transactions[_currentRowIndex];
            CurrentRowInfo.Text = $"Row {_currentRowIndex + 1} of {_transactions.Count}";

            var details = $"Date: {transaction.Date?.ToString("dd/MM/yyyy") ?? "N/A"}\n" +
                         $"Amount: {transaction.Amount?.ToString("C") ?? "N/A"}\n" +
                         $"Remarks: {transaction.TransactionRemarks}\n" +
                         $"Description: {transaction.Description}";            CurrentTransactionDetails.Text = details;
        }
        
        private void UpdateSuggestedRules()
        {
            SuggestedRulesListBox.Items.Clear();

            if (_currentRowIndex < 0 || _currentRowIndex >= _transactions.Count || _ruleEngine == null)
                return;

            var transaction = _transactions[_currentRowIndex];
            var matchingRules = _ruleEngine.FindAllMatchingRules(transaction);

            foreach (var match in matchingRules.Take(5)) // Show top 5 matches
            {
                SuggestedRulesListBox.Items.Add(new SuggestedRuleItem
                {
                    Rule = match.Rule,
                    DisplayText = $"{match.Rule.Comment} (Priority: {match.Rule.Priority})"
                });
            }

            // Select the first rule by default if any suggestions are available
            if (SuggestedRulesListBox.Items.Count > 0)
            {
                SuggestedRulesListBox.SelectedIndex = 0;
            }

            ApplySuggestedRuleButton.IsEnabled = SuggestedRulesListBox.Items.Count > 0;
        }

        #endregion

        #region Manual Entry Operations

        private void ManualEntry_Click(object sender, RoutedEventArgs e)
        {
            ManualCommentTextBox.Focus();
        }

        private void ApplyManualComment_Click(object sender, RoutedEventArgs e)
        {
            if (_currentRowIndex < 0 || _currentRowIndex >= _transactions.Count)
                return;

            var comment = ManualCommentTextBox.Text.Trim();
            if (string.IsNullOrWhiteSpace(comment))
                return;

            var transaction = _transactions[_currentRowIndex];
            var oldComment = transaction.MyComments;

            // Add to undo stack
            _undoStack.Push(new UndoAction
            {
                RowIndex = _currentRowIndex,
                OldComment = oldComment,
                NewComment = comment,
                ActionType = UndoActionType.ManualEntry
            });

            transaction.MyComments = comment;
            transaction.IsRuleApplied = false;

            ManualCommentTextBox.Clear();
            NextRow_Click(sender, e);
        }

        #endregion

        #region Rule Application

        private void ApplyAllRules_Click(object sender, RoutedEventArgs e)
        {
            if (_ruleEngine == null || _transactions.Count == 0)
                return;

            var result = MessageBox.Show(
                "This will apply all rules to unclassified transactions. Continue?",
                "Apply All Rules",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result != MessageBoxResult.Yes)
                return;

            try
            {
                StatusText.Text = "Applying rules...";
                ProgressBar.Visibility = Visibility.Visible;

                var appliedCount = _ruleEngine.ApplyRulesToTransactions(_transactions.ToList(),
                    (current, total) =>
                    {
                        Dispatcher.Invoke(() =>
                        {
                            ProgressText.Text = $"Processing {current} of {total}";
                        });
                    });

                ProgressBar.Visibility = Visibility.Collapsed;
                StatusText.Text = $"Applied rules to {appliedCount} transactions";
                UpdateUI();
            }
            catch (Exception ex)
            {
                ProgressBar.Visibility = Visibility.Collapsed;
                StatusText.Text = "Error applying rules";
                MessageBox.Show($"Error applying rules: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SuggestedRulesListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplySuggestedRuleButton.IsEnabled = SuggestedRulesListBox.SelectedItem != null;
        }

        private void ApplySuggestedRule_Click(object sender, RoutedEventArgs e)
        {
            if (SuggestedRulesListBox.SelectedItem is not SuggestedRuleItem selectedItem ||
                _currentRowIndex < 0 || _currentRowIndex >= _transactions.Count)
                return;

            var transaction = _transactions[_currentRowIndex];
            var oldComment = transaction.MyComments;

            // Add to undo stack
            _undoStack.Push(new UndoAction
            {
                RowIndex = _currentRowIndex,
                OldComment = oldComment,
                NewComment = selectedItem.Rule.Comment,
                ActionType = UndoActionType.RuleApplication
            });

            transaction.MyComments = selectedItem.Rule.Comment;
            transaction.IsRuleApplied = true;
            selectedItem.Rule.IncrementUsage();

            NextRow_Click(sender, e);
        }

        #endregion

        #region Other Event Handlers

        private void BankStatementDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (BankStatementDataGrid.SelectedIndex >= 0 && BankStatementDataGrid.SelectedIndex < _transactions.Count)
            {
                SetCurrentRow(BankStatementDataGrid.SelectedIndex);
            }
        }

        private void ClearClassifications_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "This will clear all classifications. Are you sure?",
                "Clear Classifications",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                foreach (var transaction in _transactions)
                {
                    transaction.MyComments = string.Empty;
                    transaction.IsRuleApplied = false;
                }
                UpdateUI();
            }
        }

        private void AnalyzeReferences_Click(object sender, RoutedEventArgs e)
        {
            var folderDialog = new OpenFileDialog
            {
                Title = "Select Directory Containing Reference Files",
                CheckFileExists = false,
                CheckPathExists = true,
                FileName = "Select Folder"
            };

            if (folderDialog.ShowDialog() == true)
            {
                var directory = Path.GetDirectoryName(folderDialog.FileName);
                if (!string.IsNullOrEmpty(directory))
                {
                    AnalyzeReferenceFilesInBackground(directory);
                }
            }
        }

        private async void AnalyzeReferenceFilesInBackground(string directory)
        {
            try
            {
                StatusText.Text = "Analyzing reference files...";

                await System.Threading.Tasks.Task.Run(() =>
                {
                    var suggestedRules = _patternAnalyzer.AnalyzeReferenceFiles(directory);

                    Dispatcher.Invoke(() =>
                    {
                        // Add suggested rules to the suggestions list
                        // This would be implemented in a more sophisticated way in a full application
                        StatusText.Text = $"Found {suggestedRules.Count} suggested rules from reference files";
                    });
                });
            }
            catch (Exception ex)
            {
                StatusText.Text = "Error analyzing reference files";
                MessageBox.Show($"Error analyzing reference files: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Undo_Click(object sender, RoutedEventArgs e)
        {
            if (_undoStack.Count == 0) return;

            var undoAction = _undoStack.Pop();
            if (undoAction.RowIndex >= 0 && undoAction.RowIndex < _transactions.Count)
            {
                _transactions[undoAction.RowIndex].MyComments = undoAction.OldComment;
                _transactions[undoAction.RowIndex].IsRuleApplied = false;
            }

            UpdateUI();
        }

        private void ShowShortcuts_Click(object sender, RoutedEventArgs e)
        {
            var shortcuts = "Keyboard Shortcuts:\n\n" +
                           "File Operations:\n" +
                           "Ctrl+O - Open Bank Statement\n" +
                           "Ctrl+S - Save\n" +
                           "Ctrl+Shift+S - Save As\n\n" +
                           "Rule Operations:\n" +
                           "Ctrl+N - Create New Rule\n" +
                           "Ctrl+R - Manage Rules\n" +
                           "F5 - Apply All Rules\n\n" +
                           "Processing:\n" +
                           "F2 - Start Processing\n" +
                           "F1 - Previous Row\n" +
                           "F3 - Next Row\n" +
                           "F4 - Skip Row";

            MessageBox.Show(shortcuts, "Keyboard Shortcuts", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void About_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("Bank Statement Classification Tool\n\nVersion 1.0\n\nAutomatically classify bank transactions using user-defined rules.",
                "About", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        // Event handlers for the Quick Rule Creator
        private void QuickCreateRule_Click(object sender, RoutedEventArgs e)
        {
            var conditions = QuickRuleConditionsTextBox.Text?.Trim();
            var classification = QuickRuleClassificationTextBox.Text?.Trim();

            if (string.IsNullOrEmpty(conditions))
            {
                QuickRuleStatusText.Text = "Please enter conditions first.";
                QuickRuleStatusText.Foreground = System.Windows.Media.Brushes.Orange;
                QuickRuleStatusText.Visibility = Visibility.Visible;
                return;
            }

            if (string.IsNullOrEmpty(classification))
            {
                QuickRuleStatusText.Text = "Please enter what to classify matching transactions as.";
                QuickRuleStatusText.Foreground = System.Windows.Media.Brushes.Orange;
                QuickRuleStatusText.Visibility = Visibility.Visible;
                return;
            }

            try
            {
                // Combine conditions and classification into a natural language format
                var combinedInput = $"{conditions} should be {classification}";

                // Parse the combined rule
                var parsedRule = ParseNaturalLanguageRule(combinedInput);

                if (parsedRule != null)
                {
                    // Auto-generate rule name if not provided
                    if (string.IsNullOrEmpty(parsedRule.Name))
                    {
                        parsedRule.Name = GenerateRuleName(parsedRule);
                    }

                    // Save the rule
                    _ruleManager.AddRule(parsedRule);
                    _ruleManager.SaveRules();

                    QuickRuleStatusText.Text = $"✅ Rule '{parsedRule.Name}' created successfully!";
                    QuickRuleStatusText.Foreground = System.Windows.Media.Brushes.Green;
                    QuickRuleStatusText.Visibility = Visibility.Visible;

                    // Clear the inputs
                    QuickRuleConditionsTextBox.Text = "";
                    QuickRuleClassificationTextBox.Text = "";

                    // Update rule engine
                    UpdateRuleEngine();
                    UpdateSuggestedRules();
                }
                else
                {
                    QuickRuleStatusText.Text = "❌ Could not understand the conditions. Try rephrasing or use the Advanced Rule Builder.";
                    QuickRuleStatusText.Foreground = System.Windows.Media.Brushes.Red;
                    QuickRuleStatusText.Visibility = Visibility.Visible;
                }
            }
            catch (Exception ex)
            {
                QuickRuleStatusText.Text = $"❌ Error creating rule: {ex.Message}";
                QuickRuleStatusText.Foreground = System.Windows.Media.Brushes.Red;
                QuickRuleStatusText.Visibility = Visibility.Visible;
            }
        }

        private static string GenerateRuleName(ClassificationRule rule)
        {
            if (rule.Conditions.Count == 0)
                return "New Rule";

            var firstCondition = rule.Conditions[0];
            var baseName = "";

            // Generate name based on the first condition
            switch (firstCondition.ColumnName.ToLower())
            {
                case "description":
                case "transactionremarks":
                    baseName = $"Description {firstCondition.Operator} {firstCondition.Value}";
                    break;
                case "amount":
                case "debitamount":
                case "creditamount":
                    baseName = $"Amount {firstCondition.Operator} {firstCondition.Value}";
                    break;
                case "reference":
                case "referencenumber":
                    baseName = $"Reference {firstCondition.Operator} {firstCondition.Value}";
                    break;
                default:
                    baseName = $"{firstCondition.ColumnName} {firstCondition.Operator} {firstCondition.Value}";
                    break;
            }

            // Add condition count if multiple
            if (rule.Conditions.Count > 1)
            {
                baseName += $" (+{rule.Conditions.Count - 1} more)";
            }

            // Truncate if too long
            if (baseName.Length > 50)
            {
                baseName = baseName[..47] + "...";
            }

            return baseName;
        }



        private ClassificationRule? ParseNaturalLanguageRule(string input)
        {
            var rule = new ClassificationRule();

            // Text cleaning: Remove trailing punctuation
            input = CleanConditionText(input);

            // Extract classification comment (what the transaction should be classified as)
            // Fixed regex: capture everything after "should be" until end of string
            var shouldBeMatch = System.Text.RegularExpressions.Regex.Match(input, @"should be (.+)$", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            if (shouldBeMatch.Success)
            {
                rule.Comment = shouldBeMatch.Groups[1].Value.Trim();
                rule.Name = $"Auto: {rule.Comment}";
            }
            else
            {
                // Try alternative patterns - also fixed to capture complete phrase
                var classifyAsMatch = System.Text.RegularExpressions.Regex.Match(input, @"classify as (.+)$", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                if (classifyAsMatch.Success)
                {
                    rule.Comment = classifyAsMatch.Groups[1].Value.Trim();
                    rule.Name = $"Auto: {rule.Comment}";
                }
            }

            if (string.IsNullOrEmpty(rule.Comment))
                return null;

            rule.Description = $"Generated from: {input}";

            // Parse conditions
            var conditions = new List<RuleCondition>();

            // Pattern: "containing X" or "contains X"
            var containsMatches = System.Text.RegularExpressions.Regex.Matches(input, @"(?:containing|contains)\s+['""]?([^'""]+?)['""]?(?:\s|$)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            foreach (System.Text.RegularExpressions.Match match in containsMatches)
            {
                var value = match.Groups[1].Value.Trim();
                // Remove spaces for multi-word values as specified in requirements
                var processedValue = value.Replace(" ", "");
                conditions.Add(new RuleCondition
                {
                    ColumnName = "TransactionRemarks",
                    Operator = ComparisonOperator.CONTAINS,
                    Value = processedValue,
                    IsCaseSensitive = false
                });
            }

            // Smart interpretation: "amount greater than X" defaults to deposit unless specified otherwise
            var amountGreaterMatches = System.Text.RegularExpressions.Regex.Matches(input, @"(?:amount|deposit)\s+(?:greater\s+than|>)\s+([0-9,.]+)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            foreach (System.Text.RegularExpressions.Match match in amountGreaterMatches)
            {
                var value = match.Groups[1].Value.Trim();
                conditions.Add(new RuleCondition
                {
                    ColumnName = "DepositAmtINR", // Default to deposit amount for "amount" conditions
                    Operator = ComparisonOperator.GREATER_THAN,
                    Value = value,
                    IsCaseSensitive = false
                });
            }

            // Pattern: "amount greater than or equal X" or "amount >= X"
            var amountGreaterEqualMatches = System.Text.RegularExpressions.Regex.Matches(input, @"(?:amount|deposit)\s+(?:greater\s+than\s+or\s+equal|>=)\s+([0-9,.]+)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            foreach (System.Text.RegularExpressions.Match match in amountGreaterEqualMatches)
            {
                var value = match.Groups[1].Value.Trim();
                conditions.Add(new RuleCondition
                {
                    ColumnName = "DepositAmtINR",
                    Operator = ComparisonOperator.GREATER_THAN_OR_EQUAL,
                    Value = value,
                    IsCaseSensitive = false
                });
            }

            // Pattern: "amount less than X" or "amount < X" - use DepositAmtINR for deposits
            var amountLessMatches = System.Text.RegularExpressions.Regex.Matches(input, @"(?:amount|deposit)\s+(?:less\s+than|<)\s+([0-9,.]+)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            foreach (System.Text.RegularExpressions.Match match in amountLessMatches)
            {
                var value = match.Groups[1].Value.Trim();
                conditions.Add(new RuleCondition
                {
                    ColumnName = "DepositAmtINR",
                    Operator = ComparisonOperator.LESS_THAN,
                    Value = value,
                    IsCaseSensitive = false
                });
            }

            // Pattern: "amount less than or equal X" or "amount <= X"
            var amountLessEqualMatches = System.Text.RegularExpressions.Regex.Matches(input, @"(?:amount|deposit)\s+(?:less\s+than\s+or\s+equal|<=)\s+([0-9,.]+)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            foreach (System.Text.RegularExpressions.Match match in amountLessEqualMatches)
            {
                var value = match.Groups[1].Value.Trim();
                conditions.Add(new RuleCondition
                {
                    ColumnName = "DepositAmtINR",
                    Operator = ComparisonOperator.LESS_THAN_OR_EQUAL,
                    Value = value,
                    IsCaseSensitive = false
                });
            }

            // Pattern: "withdrawal greater than X" - use WithdrawalAmtINR for withdrawals
            var withdrawalGreaterMatches = System.Text.RegularExpressions.Regex.Matches(input, @"withdrawal\s+(?:greater\s+than|>)\s+([0-9,.]+)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            foreach (System.Text.RegularExpressions.Match match in withdrawalGreaterMatches)
            {
                var value = match.Groups[1].Value.Trim();
                conditions.Add(new RuleCondition
                {
                    ColumnName = "WithdrawalAmtINR",
                    Operator = ComparisonOperator.GREATER_THAN,
                    Value = value,
                    IsCaseSensitive = false
                });
            }

            // Pattern: "withdrawal greater than or equal X"
            var withdrawalGreaterEqualMatches = System.Text.RegularExpressions.Regex.Matches(input, @"withdrawal\s+(?:greater\s+than\s+or\s+equal|>=)\s+([0-9,.]+)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            foreach (System.Text.RegularExpressions.Match match in withdrawalGreaterEqualMatches)
            {
                var value = match.Groups[1].Value.Trim();
                conditions.Add(new RuleCondition
                {
                    ColumnName = "WithdrawalAmtINR",
                    Operator = ComparisonOperator.GREATER_THAN_OR_EQUAL,
                    Value = value,
                    IsCaseSensitive = false
                });
            }

            // Pattern: "withdrawal less than X"
            var withdrawalLessMatches = System.Text.RegularExpressions.Regex.Matches(input, @"withdrawal\s+(?:less\s+than|<)\s+([0-9,.]+)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            foreach (System.Text.RegularExpressions.Match match in withdrawalLessMatches)
            {
                var value = match.Groups[1].Value.Trim();
                conditions.Add(new RuleCondition
                {
                    ColumnName = "WithdrawalAmtINR",
                    Operator = ComparisonOperator.LESS_THAN,
                    Value = value,
                    IsCaseSensitive = false
                });
            }

            // Pattern: "withdrawal less than or equal X"
            var withdrawalLessEqualMatches = System.Text.RegularExpressions.Regex.Matches(input, @"withdrawal\s+(?:less\s+than\s+or\s+equal|<=)\s+([0-9,.]+)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            foreach (System.Text.RegularExpressions.Match match in withdrawalLessEqualMatches)
            {
                var value = match.Groups[1].Value.Trim();
                conditions.Add(new RuleCondition
                {
                    ColumnName = "WithdrawalAmtINR",
                    Operator = ComparisonOperator.LESS_THAN_OR_EQUAL,
                    Value = value,
                    IsCaseSensitive = false
                });
            }

            // Pattern: "balance greater than X"
            var balanceGreaterMatches = System.Text.RegularExpressions.Regex.Matches(input, @"balance\s+(?:greater\s+than|>)\s+([0-9,.]+)", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            foreach (System.Text.RegularExpressions.Match match in balanceGreaterMatches)
            {
                var value = match.Groups[1].Value.Trim();
                conditions.Add(new RuleCondition
                {
                    ColumnName = "BalanceINR",
                    Operator = ComparisonOperator.GREATER_THAN,
                    Value = value,
                    IsCaseSensitive = false
                });
            }

            rule.Conditions = conditions;

            // Detect logical operator from input
            if (input.Contains(" or ", StringComparison.OrdinalIgnoreCase) ||
                input.Contains(" OR ", StringComparison.Ordinal))
            {
                rule.LogicalOperator = LogicalOperator.OR;
            }
            else
            {
                rule.LogicalOperator = LogicalOperator.AND; // Default to AND
            }

            return conditions.Any() ? rule : null;
        }

        /// <summary>
        /// Cleans condition text by removing trailing punctuation and normalizing whitespace
        /// </summary>
        private string CleanConditionText(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return string.Empty;

            // Remove trailing punctuation
            text = text.TrimEnd('.', '!', '?', ':', ';', ',');

            // Normalize whitespace
            text = System.Text.RegularExpressions.Regex.Replace(text, @"\s+", " ").Trim();

            return text;
        }

        private void CreateFastRule_Click(object sender, RoutedEventArgs e)
        {
            var values = FastRuleValueTextBox.Text?.Trim();
            if (string.IsNullOrEmpty(values))
            {
                ToastNotificationService.Instance.ShowWarning("Please enter search terms", "Enter values like 'SALARY', 'ATM', or 'TRANSFER'");
                return;
            }

            try
            {
                var searchTerms = values.Split(',').Select(v => v.Trim()).Where(v => !string.IsNullOrEmpty(v)).ToList();
                var createdCount = 0;

                foreach (var term in searchTerms)
                {
                    var rule = new ClassificationRule
                    {
                        Name = $"Auto: {term}",
                        Comment = term,
                        Description = $"Fast rule for transactions containing '{term}'",
                        Conditions = new List<RuleCondition>
                        {
                            new RuleCondition
                            {
                                ColumnName = "TransactionRemarks",
                                Operator = ComparisonOperator.CONTAINS,
                                Value = term,
                                IsCaseSensitive = false
                            }
                        },
                        LogicalOperator = LogicalOperator.AND,
                        Priority = 50,
                        IsEnabled = true
                    };

                    _ruleManager.AddRule(rule);
                    createdCount++;
                }

                _ruleManager.SaveRules();
                UpdateRuleEngine();
                UpdateSuggestedRules();

                FastRuleValueTextBox.Clear();
                ToastNotificationService.Instance.ShowSuccess($"Created {createdCount} rule(s)", $"Successfully created rules for: {string.Join(", ", searchTerms)}");
            }
            catch (Exception ex)
            {
                ToastNotificationService.Instance.ShowError("Error creating rules", ex.Message);
            }
        }

        private void FastRuleTemplate_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string template)
            {
                FastRuleValueTextBox.Text = template;
                FastRuleValueTextBox.Focus();
                FastRuleValueTextBox.SelectAll();
            }
        }

        private void TestRule_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("Please use the 'Create New Rule' button in the toolbar or menu to open the rule creation dialog.",
                "Use Rule Creation Dialog", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void SaveRule_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("Please use the 'Create New Rule' button in the toolbar or menu to open the rule creation dialog.",
                "Use Rule Creation Dialog", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        #endregion

        #region Cleanup

        protected override void OnClosed(EventArgs e)
        {
            _excelService?.Dispose();
            base.OnClosed(e);
        }

        #endregion
    }

    #region Helper Classes

    public class SuggestedRuleItem
    {
        public ClassificationRule Rule { get; set; } = null!;
        public string DisplayText { get; set; } = string.Empty;

        public override string ToString()
        {
            return DisplayText;
        }
    }

    public class UndoAction
    {
        public int RowIndex { get; set; }
        public string OldComment { get; set; } = string.Empty;
        public string NewComment { get; set; } = string.Empty;
        public UndoActionType ActionType { get; set; }
    }

    public enum UndoActionType
    {
        ManualEntry,
        RuleApplication
    }

    #endregion
}