<UserControl x:Class="Accountinghelper.Views.ToastNotification"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Name="ToastControl"
             Unloaded="ToastNotification_Unloaded">
    
    <UserControl.Resources>
        <!-- Success Toast Style -->
        <Style x:Key="SuccessToastStyle" TargetType="Border">
            <Setter Property="Background" Value="#4CAF50"/>
            <Setter Property="BorderBrush" Value="#45A049"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="4" Opacity="0.3" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Warning Toast Style -->
        <Style x:Key="WarningToastStyle" TargetType="Border">
            <Setter Property="Background" Value="#FF9800"/>
            <Setter Property="BorderBrush" Value="#F57C00"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="4" Opacity="0.3" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Error Toast Style -->
        <Style x:Key="ErrorToastStyle" TargetType="Border">
            <Setter Property="Background" Value="#F44336"/>
            <Setter Property="BorderBrush" Value="#D32F2F"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="4" Opacity="0.3" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Info Toast Style -->
        <Style x:Key="InfoToastStyle" TargetType="Border">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="BorderBrush" Value="#1976D2"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="4" Opacity="0.3" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Fade In Animation -->
        <Storyboard x:Key="FadeInAnimation">
            <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.3"/>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)" 
                           From="-50" To="0" Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <QuadraticEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>

        <!-- Fade Out Animation -->
        <Storyboard x:Key="FadeOutAnimation">
            <DoubleAnimation Storyboard.TargetProperty="Opacity" From="1" To="0" Duration="0:0:0.3"/>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)" 
                           From="0" To="-30" Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <QuadraticEase EasingMode="EaseIn"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>
    </UserControl.Resources>

    <Border Name="ToastBorder" 
            Opacity="0"
            RenderTransformOrigin="0.5,0.5">
        <Border.RenderTransform>
            <TranslateTransform/>
        </Border.RenderTransform>
        
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- Icon -->
            <TextBlock Name="IconText" 
                       Grid.Column="0"
                       FontSize="16" 
                       FontWeight="Bold"
                       Foreground="White"
                       VerticalAlignment="Center"
                       Margin="0,0,12,0"/>

            <!-- Message -->
            <StackPanel Grid.Column="1" VerticalAlignment="Center">
                <TextBlock Name="TitleText" 
                           FontWeight="Bold" 
                           FontSize="14"
                           Foreground="White"
                           TextWrapping="Wrap"/>
                <TextBlock Name="MessageText" 
                           FontSize="12"
                           Foreground="White"
                           TextWrapping="Wrap"
                           Margin="0,2,0,0"/>
            </StackPanel>

            <!-- Close Button -->
            <Button Name="CloseButton"
                    Grid.Column="2"
                    Content="✕"
                    Width="20"
                    Height="20"
                    Background="Transparent"
                    BorderThickness="0"
                    Foreground="White"
                    FontWeight="Bold"
                    Cursor="Hand"
                    VerticalAlignment="Top"
                    Margin="12,0,0,0"
                    Click="CloseButton_Click"
                    ToolTip="Close notification"/>
        </Grid>
    </Border>
</UserControl>
