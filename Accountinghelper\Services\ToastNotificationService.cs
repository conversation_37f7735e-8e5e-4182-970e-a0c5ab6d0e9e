using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Accountinghelper.Views;

namespace Accountinghelper.Services
{
    public class ToastNotificationService
    {
        private static ToastNotificationService? _instance;
        private readonly List<ToastNotification> _activeToasts = new();
        private Panel? _toastContainer;

        public static ToastNotificationService Instance => _instance ??= new ToastNotificationService();

        private ToastNotificationService() { }

        public void Initialize(Panel toastContainer)
        {
            _toastContainer = toastContainer;
        }

        public void ShowSuccess(string title, string message = "")
        {
            ShowToast(title, message, ToastType.Success);
        }

        public void ShowInfo(string title, string message = "")
        {
            ShowToast(title, message, ToastType.Info);
        }

        public void ShowWarning(string title, string message = "")
        {
            ShowToast(title, message, ToastType.Warning);
        }

        public void ShowError(string title, string message = "")
        {
            ShowToast(title, message, ToastType.Error);
        }

        private void ShowToast(string title, string message, ToastType type)
        {
            if (_toastContainer == null)
            {
                // Toast container not available - skip showing toast
                return;
            }

            Application.Current.Dispatcher.Invoke(() =>
            {
                var toast = new ToastNotification();
                toast.Closed += (s, e) => RemoveToast(toast);
                
                _activeToasts.Add(toast);
                _toastContainer.Children.Add(toast);
                
                // Position toast (stack from top)
                Canvas.SetTop(toast, _activeToasts.Count * 80);
                Canvas.SetRight(toast, 20);
                
                toast.Show(title, message, type);
                
                // Limit number of visible toasts
                if (_activeToasts.Count > 5)
                {
                    var oldestToast = _activeToasts.First();
                    oldestToast.Close();
                }
            });
        }

        private void RemoveToast(ToastNotification toast)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                _activeToasts.Remove(toast);
                _toastContainer?.Children.Remove(toast);
                
                // Reposition remaining toasts
                for (int i = 0; i < _activeToasts.Count; i++)
                {
                    Canvas.SetTop(_activeToasts[i], i * 80);
                }
            });
        }

        public void ClearAll()
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                foreach (var toast in _activeToasts.ToList())
                {
                    toast.Close();
                }
            });
        }
    }
}
