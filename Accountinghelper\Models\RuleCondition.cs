using System;
using System.Globalization;
using System.Text.Json.Serialization;

namespace Accountinghelper.Models
{
    public class RuleCondition
    {
        public string ColumnName { get; set; } = string.Empty;
        public ComparisonOperator Operator { get; set; } = ComparisonOperator.CONTAINS;
        public string Value { get; set; } = string.Empty;
        public bool IsCaseSensitive { get; set; } = false;

        public bool Evaluate(BankTransaction transaction)
        {
            var columnValue = transaction.GetValueByColumnName(ColumnName);
            
            if (columnValue == null)
                return false;

            var stringValue = columnValue.ToString() ?? string.Empty;
            var compareValue = Value;

            if (!IsCaseSensitive)
            {
                stringValue = stringValue.ToLowerInvariant();
                compareValue = compareValue.ToLowerInvariant();
            }

            return Operator switch
            {
                ComparisonOperator.CONTAINS => stringValue.Contains(compareValue),
                ComparisonOperator.EQUALS => stringValue.Equals(compareValue),
                ComparisonOperator.STARTS_WITH => stringValue.StartsWith(compareValue),
                ComparisonOperator.ENDS_WITH => stringValue.EndsWith(compareValue),
                ComparisonOperator.GREATER_THAN => EvaluateNumericComparison(columnValue, Value, (a, b) => a > b),
                ComparisonOperator.LESS_THAN => EvaluateNumericComparison(columnValue, Value, (a, b) => a < b),
                ComparisonOperator.GREATER_THAN_OR_EQUAL => EvaluateNumericComparison(columnValue, Value, (a, b) => a >= b),
                ComparisonOperator.LESS_THAN_OR_EQUAL => EvaluateNumericComparison(columnValue, Value, (a, b) => a <= b),
                ComparisonOperator.NOT_EQUALS => !stringValue.Equals(compareValue),
                ComparisonOperator.NOT_CONTAINS => !stringValue.Contains(compareValue),
                _ => false
            };
        }

        private bool EvaluateNumericComparison(object columnValue, string compareValue, Func<decimal, decimal, bool> comparison)
        {
            decimal columnDecimal = 0;
            decimal compareDecimal = 0;

            // Try to convert column value to decimal
            if (columnValue is decimal dec)
            {
                columnDecimal = dec;
            }
            else if (columnValue is int intVal)
            {
                columnDecimal = intVal;
            }
            else if (columnValue is double doubleVal)
            {
                columnDecimal = (decimal)doubleVal;
            }
            else if (columnValue is float floatVal)
            {
                columnDecimal = (decimal)floatVal;
            }
            else if (!decimal.TryParse(columnValue?.ToString(), NumberStyles.Any, CultureInfo.InvariantCulture, out columnDecimal))
            {
                return false;
            }

            // Clean and try to convert compare value to decimal (handle currency formatting)
            var cleanCompareValue = compareValue?.Replace(",", "").Replace("$", "").Replace("₹", "").Trim();
            if (!decimal.TryParse(cleanCompareValue, NumberStyles.Any, CultureInfo.InvariantCulture, out compareDecimal))
            {
                return false;
            }

            return comparison(columnDecimal, compareDecimal);
        }

        /// <summary>
        /// Checks if the given column name represents a numerical column that supports numerical comparisons
        /// </summary>
        public static bool IsNumericalColumn(string columnName)
        {
            var normalizedName = columnName.Replace(" ", "").ToLowerInvariant();

            return normalizedName switch
            {
                // Amount columns
                "amount" => true,
                "debitamount" => true,
                "creditamount" => true,
                "balance" => true,

                // Excel file specific columns
                "withdrawalamtinr" => true,
                "depositamtinr" => true,
                "balanceinr" => true,

                // Alternative names
                "withdrawal" => true,
                "deposit" => true,
                "debit" => true,
                "credit" => true,
                "dr" => true,
                "cr" => true,
                "withdrawalamt" => true,
                "depositamt" => true,
                "debitamt" => true,
                "creditamt" => true,
                "bal" => true,
                "runningbalance" => true,
                "closingbalance" => true,
                "txnamount" => true,
                "transactionamount" => true,

                _ => false
            };
        }

        /// <summary>
        /// Checks if the given operator is a numerical comparison operator
        /// </summary>
        public static bool IsNumericalOperator(ComparisonOperator op)
        {
            return op == ComparisonOperator.GREATER_THAN ||
                   op == ComparisonOperator.LESS_THAN ||
                   op == ComparisonOperator.GREATER_THAN_OR_EQUAL ||
                   op == ComparisonOperator.LESS_THAN_OR_EQUAL;
        }

        public override string ToString()
        {
            var operatorText = Operator switch
            {
                ComparisonOperator.CONTAINS => "CONTAINS",
                ComparisonOperator.EQUALS => "EQUALS",
                ComparisonOperator.STARTS_WITH => "STARTS WITH",
                ComparisonOperator.ENDS_WITH => "ENDS WITH",
                ComparisonOperator.GREATER_THAN => ">",
                ComparisonOperator.LESS_THAN => "<",
                ComparisonOperator.GREATER_THAN_OR_EQUAL => ">=",
                ComparisonOperator.LESS_THAN_OR_EQUAL => "<=",
                ComparisonOperator.NOT_EQUALS => "NOT EQUALS",
                ComparisonOperator.NOT_CONTAINS => "NOT CONTAINS",
                _ => "UNKNOWN"
            };

            return $"{ColumnName} {operatorText} \"{Value}\"";
        }
    }

    public enum ComparisonOperator
    {
        CONTAINS,
        EQUALS,
        STARTS_WITH,
        ENDS_WITH,
        GREATER_THAN,
        LESS_THAN,
        GREATER_THAN_OR_EQUAL,
        LESS_THAN_OR_EQUAL,
        NOT_EQUALS,
        NOT_CONTAINS
    }
}
