<Window x:Class="Accountinghelper.Views.RuleManagementDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Rule Management" Height="600" Width="900"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">
    
    <Window.Resources>
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>
        
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="MinWidth" Value="100"/>
        </Style>
    </Window.Resources>

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
            <TextBlock Text="Manage Classification Rules" Style="{StaticResource HeaderTextStyle}" VerticalAlignment="Center"/>
            <TextBlock Text="💡 Tip: Drag rows to reorder rules, double-click cells to edit inline"
                       FontStyle="Italic"
                       Foreground="Gray"
                       Margin="20,0,0,0"
                       VerticalAlignment="Center"/>
        </StackPanel>

        <!-- Rules List -->
        <DataGrid Grid.Row="1"
                  Name="RulesDataGrid"
                  AutoGenerateColumns="False"
                  CanUserAddRows="False"
                  CanUserDeleteRows="False"
                  SelectionMode="Single"
                  SelectionChanged="RulesDataGrid_SelectionChanged"
                  AllowDrop="True"
                  Drop="RulesDataGrid_Drop"
                  DragOver="RulesDataGrid_DragOver"
                  PreviewMouseLeftButtonDown="RulesDataGrid_PreviewMouseLeftButtonDown"
                  MouseMove="RulesDataGrid_MouseMove"
                  CellEditEnding="RulesDataGrid_CellEditEnding"
                  BeginningEdit="RulesDataGrid_BeginningEdit"
                  Margin="0,10"
                  ToolTip="Drag rows to reorder rules by priority. Double-click cells to edit inline.">

            <DataGrid.Columns>
                <!-- Drag Handle Column -->
                <DataGridTemplateColumn Header="⋮⋮" Width="30" CanUserResize="False" CanUserSort="False">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock Text="⋮⋮"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="Gray"
                                       Cursor="SizeAll"
                                       ToolTip="Drag to reorder"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>

                <!-- Enabled Column -->
                <DataGridCheckBoxColumn Header="✓" Binding="{Binding IsEnabled}" Width="40" CanUserSort="False">
                    <DataGridCheckBoxColumn.HeaderTemplate>
                        <DataTemplate>
                            <TextBlock Text="✓" ToolTip="Enable/Disable Rule"/>
                        </DataTemplate>
                    </DataGridCheckBoxColumn.HeaderTemplate>
                </DataGridCheckBoxColumn>

                <!-- Priority Column (Editable) -->
                <DataGridTextColumn Header="Priority" Binding="{Binding Priority}" Width="70" CanUserSort="False">
                    <DataGridTextColumn.HeaderTemplate>
                        <DataTemplate>
                            <TextBlock Text="Priority" ToolTip="Higher numbers = higher priority (applied first)"/>
                        </DataTemplate>
                    </DataGridTextColumn.HeaderTemplate>
                </DataGridTextColumn>

                <!-- Name Column (Editable) -->
                <DataGridTextColumn Header="Name" Binding="{Binding Name}" Width="150">
                    <DataGridTextColumn.HeaderTemplate>
                        <DataTemplate>
                            <TextBlock Text="Name" ToolTip="Rule name (double-click to edit)"/>
                        </DataTemplate>
                    </DataGridTextColumn.HeaderTemplate>
                </DataGridTextColumn>

                <!-- Comment Column (Editable) -->
                <DataGridTextColumn Header="Classification" Binding="{Binding Comment}" Width="200">
                    <DataGridTextColumn.HeaderTemplate>
                        <DataTemplate>
                            <TextBlock Text="Classification" ToolTip="What transactions are classified as (double-click to edit)"/>
                        </DataTemplate>
                    </DataGridTextColumn.HeaderTemplate>
                </DataGridTextColumn>

                <!-- Description Column (Editable) -->
                <DataGridTextColumn Header="Description" Binding="{Binding Description}" Width="200">
                    <DataGridTextColumn.HeaderTemplate>
                        <DataTemplate>
                            <TextBlock Text="Description" ToolTip="Rule description (double-click to edit)"/>
                        </DataTemplate>
                    </DataGridTextColumn.HeaderTemplate>
                </DataGridTextColumn>

                <!-- Statistics Columns (Read-only) -->
                <DataGridTextColumn Header="Applied" Binding="{Binding TimesApplied}" Width="70" IsReadOnly="True">
                    <DataGridTextColumn.HeaderTemplate>
                        <DataTemplate>
                            <TextBlock Text="Applied" ToolTip="Number of times this rule has been applied"/>
                        </DataTemplate>
                    </DataGridTextColumn.HeaderTemplate>
                </DataGridTextColumn>

                <DataGridTextColumn Header="Last Used" Binding="{Binding LastAppliedDate, StringFormat=dd/MM/yyyy}" Width="80" IsReadOnly="True">
                    <DataGridTextColumn.HeaderTemplate>
                        <DataTemplate>
                            <TextBlock Text="Last Used" ToolTip="Date when this rule was last applied"/>
                        </DataTemplate>
                    </DataGridTextColumn.HeaderTemplate>
                </DataGridTextColumn>

                <DataGridTextColumn Header="Confidence" Binding="{Binding ConfidenceScore, StringFormat=P1}" Width="80" IsReadOnly="True">
                    <DataGridTextColumn.HeaderTemplate>
                        <DataTemplate>
                            <TextBlock Text="Confidence" ToolTip="Confidence score for auto-generated rules"/>
                        </DataTemplate>
                    </DataGridTextColumn.HeaderTemplate>
                </DataGridTextColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!-- Rule Details -->
        <GroupBox Grid.Row="2" Header="Rule Details" Margin="0,10" MaxHeight="150">
            <ScrollViewer>
                <StackPanel Name="RuleDetailsPanel">
                    <TextBlock Name="RuleNameText" FontWeight="Bold" Margin="5"/>
                    <TextBlock Name="RuleDescriptionText" Margin="5" TextWrapping="Wrap"/>
                    <TextBlock Name="RuleConditionsText" Margin="5" TextWrapping="Wrap"/>
                </StackPanel>
            </ScrollViewer>
        </GroupBox>

        <!-- Buttons -->
        <Grid Grid.Row="3" Margin="0,10,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- Left side buttons -->
            <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Left">
                <Button Name="SaveChangesButton"
                        Content="💾 Save Changes"
                        Style="{StaticResource ButtonStyle}"
                        Click="SaveChanges_Click"
                        Background="LightGreen"
                        ToolTip="Save all inline edits and changes"
                        IsEnabled="False"/>
                <Button Name="ResetOrderButton"
                        Content="🔄 Reset Order"
                        Style="{StaticResource ButtonStyle}"
                        Click="ResetOrder_Click"
                        ToolTip="Reset rule order to original priority values"/>
            </StackPanel>

            <!-- Right side buttons -->
            <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Name="NewRuleButton"
                        Content="➕ New Rule"
                        Style="{StaticResource ButtonStyle}"
                        Click="NewRule_Click"
                        ToolTip="Create a new rule"/>
                <Button Name="EditRuleButton"
                        Content="🔧 Advanced Edit"
                        Style="{StaticResource ButtonStyle}"
                        Click="EditRule_Click"
                        IsEnabled="False"
                        ToolTip="Open advanced rule editor for complex conditions"/>
                <Button Name="DuplicateRuleButton"
                        Content="📋 Duplicate"
                        Style="{StaticResource ButtonStyle}"
                        Click="DuplicateRule_Click"
                        IsEnabled="False"
                        ToolTip="Create a copy of the selected rule"/>
                <Button Name="DeleteRuleButton"
                        Content="🗑️ Delete"
                        Style="{StaticResource ButtonStyle}"
                        Click="DeleteRule_Click"
                        IsEnabled="False"
                        ToolTip="Delete the selected rule"/>
                <Button Name="TestRuleButton"
                        Content="🧪 Test"
                        Style="{StaticResource ButtonStyle}"
                        Click="TestRule_Click"
                        IsEnabled="False"
                        ToolTip="Test the selected rule against sample data"/>
                <Separator/>
                <Button Name="ExportRulesButton"
                        Content="📤 Export"
                        Style="{StaticResource ButtonStyle}"
                        Click="ExportRules_Click"
                        ToolTip="Export all rules to a file"/>
                <Button Name="ImportRulesButton"
                        Content="📥 Import"
                        Style="{StaticResource ButtonStyle}"
                        Click="ImportRules_Click"
                        ToolTip="Import rules from a file"/>
                <Button Name="CloseButton"
                        Content="✖️ Close"
                        Style="{StaticResource ButtonStyle}"
                        Click="Close_Click"
                        ToolTip="Close this dialog"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
