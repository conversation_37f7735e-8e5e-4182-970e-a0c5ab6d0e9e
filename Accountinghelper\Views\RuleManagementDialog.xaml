<Window x:Class="Accountinghelper.Views.RuleManagementDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Rule Management" Height="600" Width="900"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">
    
    <Window.Resources>
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>
        
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="MinWidth" Value="100"/>
        </Style>
    </Window.Resources>

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" Text="Manage Classification Rules" Style="{StaticResource HeaderTextStyle}"/>

        <!-- Rules List -->
        <DataGrid Grid.Row="1" 
                  Name="RulesDataGrid"
                  AutoGenerateColumns="False"
                  CanUserAddRows="False"
                  CanUserDeleteRows="False"
                  SelectionMode="Single"
                  SelectionChanged="RulesDataGrid_SelectionChanged"
                  Margin="0,10">
            
            <DataGrid.Columns>
                <DataGridCheckBoxColumn Header="Enabled" Binding="{Binding IsEnabled}" Width="70"/>
                <DataGridTextColumn Header="Name" Binding="{Binding Name}" Width="150"/>
                <DataGridTextColumn Header="Comment" Binding="{Binding Comment}" Width="200"/>
                <DataGridTextColumn Header="Priority" Binding="{Binding Priority}" Width="70"/>
                <DataGridTextColumn Header="Times Applied" Binding="{Binding TimesApplied}" Width="100"/>
                <DataGridTextColumn Header="Last Applied" Binding="{Binding LastAppliedDate, StringFormat=dd/MM/yyyy}" Width="100"/>
                <DataGridTextColumn Header="Confidence" Binding="{Binding ConfidenceScore, StringFormat=P1}" Width="80"/>
            </DataGrid.Columns>
        </DataGrid>

        <!-- Rule Details -->
        <GroupBox Grid.Row="2" Header="Rule Details" Margin="0,10" MaxHeight="150">
            <ScrollViewer>
                <StackPanel Name="RuleDetailsPanel">
                    <TextBlock Name="RuleNameText" FontWeight="Bold" Margin="5"/>
                    <TextBlock Name="RuleDescriptionText" Margin="5" TextWrapping="Wrap"/>
                    <TextBlock Name="RuleConditionsText" Margin="5" TextWrapping="Wrap"/>
                </StackPanel>
            </ScrollViewer>
        </GroupBox>

        <!-- Buttons -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,10,0,0">
            <Button Name="NewRuleButton" Content="New Rule" Style="{StaticResource ButtonStyle}" Click="NewRule_Click"/>
            <Button Name="EditRuleButton" Content="Edit Rule" Style="{StaticResource ButtonStyle}" Click="EditRule_Click" IsEnabled="False"/>
            <Button Name="DeleteRuleButton" Content="Delete Rule" Style="{StaticResource ButtonStyle}" Click="DeleteRule_Click" IsEnabled="False"/>
            <Button Name="TestRuleButton" Content="Test Rule" Style="{StaticResource ButtonStyle}" Click="TestRule_Click" IsEnabled="False"/>
            <Button Name="ExportRulesButton" Content="Export Rules" Style="{StaticResource ButtonStyle}" Click="ExportRules_Click"/>
            <Button Name="ImportRulesButton" Content="Import Rules" Style="{StaticResource ButtonStyle}" Click="ImportRules_Click"/>
            <Button Name="CloseButton" Content="Close" Style="{StaticResource ButtonStyle}" Click="Close_Click"/>
        </StackPanel>
    </Grid>
</Window>
