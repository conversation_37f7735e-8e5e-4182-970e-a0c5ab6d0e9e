using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Animation;
using System.Windows.Threading;

namespace Accountinghelper.Views
{
    public partial class ToastNotification : UserControl
    {
        private DispatcherTimer _autoCloseTimer = null!;
        private Storyboard _fadeInAnimation = null!;
        private Storyboard _fadeOutAnimation = null!;

        public event EventHandler? Closed;

        public ToastNotification()
        {
            InitializeComponent();
            InitializeAnimations();
            InitializeTimer();
        }

        private void InitializeAnimations()
        {
            _fadeInAnimation = (Storyboard)Resources["FadeInAnimation"];
            _fadeOutAnimation = (Storyboard)Resources["FadeOutAnimation"];
            
            _fadeOutAnimation.Completed += (s, e) => 
            {
                Visibility = Visibility.Collapsed;
                Closed?.Invoke(this, EventArgs.Empty);
            };
        }

        private void InitializeTimer()
        {
            _autoCloseTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(10) // Auto-dismiss after 10 seconds
            };
            _autoCloseTimer.Tick += (s, e) => Close();
        }

        public void Show(string title, string message, ToastType type = ToastType.Info)
        {
            // Set content
            TitleText.Text = title;
            MessageText.Text = message;
            
            // Set style and icon based on type
            switch (type)
            {
                case ToastType.Success:
                    ToastBorder.Style = (Style)Resources["SuccessToastStyle"];
                    IconText.Text = "✓";
                    break;
                case ToastType.Warning:
                    ToastBorder.Style = (Style)Resources["WarningToastStyle"];
                    IconText.Text = "⚠";
                    break;
                case ToastType.Error:
                    ToastBorder.Style = (Style)Resources["ErrorToastStyle"];
                    IconText.Text = "✕";
                    break;
                case ToastType.Info:
                default:
                    ToastBorder.Style = (Style)Resources["InfoToastStyle"];
                    IconText.Text = "ⓘ";
                    break;
            }

            // Show and animate
            Visibility = Visibility.Visible;
            _fadeInAnimation.Begin(ToastBorder);
            
            // Start auto-close timer
            _autoCloseTimer.Start();
        }

        public void Close()
        {
            _autoCloseTimer.Stop();
            _fadeOutAnimation.Begin(ToastBorder);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void ToastNotification_Unloaded(object sender, RoutedEventArgs e)
        {
            _autoCloseTimer?.Stop();
        }
    }

    public enum ToastType
    {
        Info,
        Success,
        Warning,
        Error
    }
}
